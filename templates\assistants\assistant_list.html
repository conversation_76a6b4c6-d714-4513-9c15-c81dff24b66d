{% extends "base/layout.html" %}
{% load static assistant_tags rating_tags %} {# Load assistant_tags and rating_tags #}
{% load i18n %} {# Load i18n for query_string tag if not already loaded globally #}

{% block title %}Manage Assistants - {{ company.name }}{% endblock %}

{% block head_extra %}
{# Link to the shared directory CSS #}
<link rel="stylesheet" href="{% static 'css/directory.css' %}">
<!-- <link rel="stylesheet" href="{% static 'css/assistant-list-dark-mode.css' %}"> -->
<link rel="stylesheet" href="{% static 'css/unified-cards-responsive.css' %}">
<!-- <link rel="stylesheet" href="{% static 'css/unified-cards-dark-mode.css' %}"> -->
<link rel="stylesheet" href="{% static 'css/modern-assistant-buttons.css' %}">
<link rel="stylesheet" href="{% static 'css/assistant-management-improved.css' %}?v=2">
<style>
    /* Keep only necessary specific styles if any, e.g., tier badges if not in directory.css */
    .tier-badge {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 0.7em;
        padding: 0.25em 0.5em;
        margin: 0.5rem;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .tier-gold { background-color: #ffc107; color: #212529; }
    .tier-silver { background-color: #adb5bd; color: #212529; }
    .tier-bronze { background-color: #cd7f32; color: #fff; }

    /* Community assistant specific styling */
    /* Override directory.css styles for all assistants in this view */
    .logo-container {
        /* !important on all properties to override directory.css */
        height: 100px !important; /* Smaller size for manage assistants view */
        width: 100px !important;
        min-height: 100px !important;
        min-width: 100px !important;
        max-height: 100px !important;
        max-width: 100px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: white !important;
        border-radius: 0.25rem !important;
        overflow: hidden !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        transition: all 0.3s ease !important;
        border: 1px solid rgba(0, 0, 0, 0.08) !important;
        position: relative !important;
        aspect-ratio: 1/1 !important;
        margin: 0 auto !important;
        transform: none !important; /* Override the transform from directory.css */
        /* Additional overrides to prevent inheritance from directory.css */
        all: revert !important;
        height: 100px !important;
        width: 100px !important;
        min-height: 100px !important;
        min-width: 100px !important;
        max-height: 100px !important;
        max-width: 100px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: white !important;
        border-radius: 0.25rem !important;
        overflow: hidden !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        transition: all 0.3s ease !important;
        border: 1px solid rgba(0, 0, 0, 0.08) !important;
        position: relative !important;
        aspect-ratio: 1/1 !important;
        margin: 0 auto !important;
        transform: none !important;
    }

    /* Override directory.css styles for all assistant images in this view */
    .logo-container img {
        max-width: 80% !important;
        max-height: 80% !important;
        width: auto !important; /* Allow natural width */
        height: auto !important; /* Allow natural height */
        object-fit: contain !important; /* Ensure image is fully contained */
        display: block !important;
        margin: auto !important; /* Center the image */
        padding: 10px !important;
        transition: all 0.3s ease !important;
        position: relative !important; /* Ensure proper stacking */
        /* Force image size constraints */
        max-inline-size: 80px !important;
        max-block-size: 80px !important;
    }

    /* Override directory.css styles for all assistant placeholders in this view */
    .logo-placeholder {
        color: #cf2e2e !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
        overflow: hidden !important; /* Prevent overflow */
        padding: 0 !important;
        background-color: rgba(240, 248, 255, 0.5) !important;
        z-index: 1 !important; /* Ensure proper stacking */
    }

    .logo-placeholder i {
        font-size: 60px !important; /* Smaller icon for manage assistants view */
        line-height: 1 !important;
        display: block !important;
        text-align: center !important;
        margin: 0 !important;
        padding: 0 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    }

    /* NUP Color Overrides for Assistant List Page */
    .btn-primary {
        background-color: #cf2e2e !important;
        border-color: #cf2e2e !important;
        color: #ffffff !important;
    }

    .btn-primary:hover,
    .btn-primary:focus,
    .btn-primary:active {
        background-color: #252638 !important;
        border-color: #252638 !important;
        color: #ffffff !important;
    }

    .btn-outline-primary {
        color: #cf2e2e !important;
        border-color: #cf2e2e !important;
        background-color: transparent !important;
    }

    .btn-outline-primary:hover,
    .btn-outline-primary:focus,
    .btn-outline-primary:active {
        background-color: #cf2e2e !important;
        border-color: #cf2e2e !important;
        color: #ffffff !important;
    }

    /* Override any blue gradients */
    .assistant-action-btn.btn-primary {
        background: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
    }

    .assistant-action-btn.btn-primary:hover {
        background: linear-gradient(135deg, #252638 0%, #1a1d2e 100%) !important;
    }

    /* Override green success buttons */
    .btn-success,
    .btn-outline-success {
        background-color: #cf2e2e !important;
        border-color: #cf2e2e !important;
        color: #ffffff !important;
    }

    .btn-success:hover,
    .btn-outline-success:hover,
    .btn-success:focus,
    .btn-outline-success:focus,
    .btn-success:active,
    .btn-outline-success:active {
        background-color: #252638 !important;
        border-color: #252638 !important;
        color: #ffffff !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h2 mb-1">Manage Assistants</h1>
            <p class="text-muted mb-0">For {{ company.name }}</p>
        </div>
        <div>
            {% if company.entity_type == 'community' %}
            <a href="{% url 'assistants:create' company_id=company.id %}" class="btn btn-primary"> {# Corrected URL name #}
                <i class="bi bi-plus-lg"></i> Create New Community Assistant
            </a>
            {% else %}
            <a href="{% url 'assistants:create' company_id=company.id %}" class="btn btn-primary"> {# Corrected URL name #}
                <i class="bi bi-plus-lg"></i> Create New Assistant
            </a>
            {% endif %}
        </div>
    </div>

    {# Filter/Search Form #}
    <form method="get" class="filter-form mb-4">
        <h5 class="mb-3"><i class="bi bi-funnel-fill me-2 text-primary"></i>Filter Assistants</h5>
        <div class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="q" class="form-label">Search Name</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" name="q" id="q" class="form-control" placeholder="Search by name..." value="{{ search_query|default:'' }}">
                </div>
            </div>
            <div class="col-md-3">
                <label for="type" class="form-label">Type</label>
                <select name="type" id="type" class="form-select">
                    <option value="">All Types</option>
                    {% for value, display in assistant_types %}
                    <option value="{{ value }}" {% if filter_type == value %}selected{% endif %}>{{ display }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-filter me-1"></i> Apply Filters
                </button>
            </div>
            {% if search_query or filter_type %}
            <div class="col-md-2 d-flex align-items-end">
                <a href="{% url 'assistants:list' company_id=company.id %}" class="btn btn-outline-secondary w-100">
                    <i class="bi bi-x-circle me-1"></i> Reset
                </a>
            </div>
            {% endif %}
        </div>
    </form>

    {# Folder Filter Links (Horizontal - Style copied from my_favorites.html) #}
    {# Removed {% if folders %} wrapper to ensure "Show All" and "Unassigned" always show #}
    <div class="folder-filter-buttons mb-4 d-flex flex-wrap align-items-center border-bottom pb-2" role="group" aria-label="Filter by folder">
        {% with current_params=request.GET.copy %}
            {% query_string current_params folder_id="" as all_url %}
            <a href="{{ all_url }}" class="btn btn-link text-decoration-none p-1 me-3 {% if not selected_folder_id or selected_folder_id == "" %}active{% endif %}"> {# Added check for empty string #}
                <i class="bi bi-grid-fill me-1"></i>Show All
            </a>
            {% query_string current_params folder_id="unassigned" as unassigned_url %}
            <a href="{{ unassigned_url }}" class="btn btn-link text-decoration-none p-1 me-3 {% if selected_folder_id == 'unassigned' %}active{% endif %}">
                <i class="bi bi-box me-1"></i>Unassigned
            </a>
            {% for folder in folders %} {# This loop correctly handles the case where folders is empty #}
                {% query_string current_params folder_id=folder.id as folder_url %}
                 <div class="d-inline-block position-relative me-3"> {# Wrapper for dropdown #}
                    <a href="{{ folder_url }}" class="btn btn-link text-decoration-none p-1 folder-filter-link {% if selected_folder_id == folder.id|stringformat:"s" %}active{% endif %}" data-folder-id="{{ folder.id }}">
                        <i class="bi bi-folder me-1"></i>{{ folder.name }}
                    </a>
                    {# Dropdown Trigger for Edit/Delete #}
                    <button class="btn btn-sm btn-link text-secondary p-0 ms-1 position-absolute top-0 end-0" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="line-height: 1;">
                        <i class="bi bi-three-dots-vertical"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        {# Add data-folder-order attribute #}
                        <li><a class="dropdown-item edit-folder-btn" href="#" data-folder-id="{{ folder.id }}" data-folder-name="{{ folder.name|escapejs }}" data-folder-order="{{ folder.order|default:0 }}"><i class="bi bi-pencil-square me-2"></i>Edit</a></li>
                        <li><a class="dropdown-item delete-folder-btn" href="#" data-folder-id="{{ folder.id }}" data-folder-name="{{ folder.name|escapejs }}"><i class="bi bi-trash me-2"></i>Delete</a></li>
                    </ul>
                </div>
            {% endfor %}
        {% endwith %}
        {# Add Folder Button - JS will trigger modal (Removed data-bs-* attributes) #}
        <button type="button" class="btn btn-sm btn-outline-secondary ms-auto js-trigger-create-folder-modal">
            <i class="bi bi-plus-circle me-1"></i> Add Folder
        </button>
    </div>
    {# End Folder Filter Links #}

    {# Main Assistant List Section #}
    <div class="mt-5">
        <h2 class="h3 mb-4 fw-bold">
            <i class="bi bi-robot me-2 text-primary"></i>All Assistants
        </h2>

        {% if grouped_assistants_list %}
            {% for folder, assistants_in_folder in grouped_assistants_list %}
                {# Display Folder Header #}
                <div class="tier-section mb-4">
                    <h3 class="h5 fw-bold">
                        {% if folder %}
                            <i class="bi bi-folder me-2"></i>{{ folder.name }}
                        {% else %}
                            <i class="bi bi-box me-2"></i>Unassigned
                        {% endif %}
                    </h3>

                    {# Display Assistants in this Folder - Add ID for JS targeting #}
                    {% if assistants_in_folder %}
                        <div class="list-group" id="folder-group-{% if folder %}{{ folder.id }}{% else %}unassigned{% endif %}"> {# Add ID #}
                            {% for assistant in assistants_in_folder %}
                            {# Add data-assistant-id to the main item for easier selection #}
                            <div class="list-group-item position-relative directory-card" data-assistant-id="{{ assistant.id }}" data-assistant-type="{{ assistant.assistant_type }}">
                                {% if assistant.tier and assistant.tier != 'standard' %}
                                <span class="badge tier-badge tier-{{ assistant.tier|lower }}">
                                    {% if assistant.tier == 'gold' %}
                                    <i class="bi bi-trophy-fill me-1"></i>Gold
                                    {% elif assistant.tier == 'silver' %}
                                    <i class="bi bi-award-fill me-1"></i>Silver
                                    {% elif assistant.tier == 'bronze' %}
                                    <i class="bi bi-award me-1"></i>Bronze
                                    {% endif %}
                                </span>
                                {% endif %}

                                <div class="row g-3">
                                    {# Column 1: Logo - Adjusted size to match company list #}
                                    <div class="col-md-3 d-flex justify-content-center align-items-center" style="padding-left: 20px;"> {# Changed col-md-2 to col-md-3 and added padding-left #}
                                        <div class="logo-container" style="width: 100px !important; height: 100px !important; min-width: 100px !important; min-height: 100px !important; max-width: 100px !important; max-height: 100px !important;">
                                            {% with logo_url=assistant.get_logo_url %}
                                                {% if logo_url %}
                                                    <img src="{{ logo_url }}" alt="{{ assistant.name }} logo"
                                                         style="max-width: 80px !important; max-height: 80px !important; width: auto !important; height: auto !important; object-fit: contain !important;"
                                                         onerror="console.error('Logo load error for {{ assistant.id }}:', this.src); this.style.display='none'; this.parentNode.innerHTML='<div class=\'logo-placeholder\'><i class=\'bi bi-robot\'></i></div>';">
                                                {% else %}
                                                    {# Wrapped placeholder icon in a div to match company card #}
                                                    <div class="logo-placeholder">
                                                        <i class="bi bi-robot"></i>
                                                    </div>
                                                {% endif %}
                                            {% endwith %}
                                        </div>
                                    </div>

                                    {# Column 2: Assistant Details - Adjusted width for better button layout #}
                                    <div class="col-md-6"> {# Changed to col-md-6 to accommodate col-md-3 buttons #}
                                        <h5 class="assistant-name">
                                            <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="text-decoration-none">{{ assistant.name }}</a>
                                        </h5>
                                        <p class="assistant-meta">
                                            <i class="bi bi-tag me-1"></i>{{ assistant.get_assistant_type_display }}
                                            {% if assistant.assistant_type == 'community' %}
                                            <span class="badge bg-secondary ms-2 community-badge">Community</span>
                                            {% else %}
                                            <i class="bi bi-cpu ms-2 me-1"></i>{{ assistant.model }}
                                            {% endif %}
                                            <span class="ms-2">
                                                <span class="badge {% if assistant.is_active %}bg-success{% else %}bg-warning text-dark{% endif %}">
                                                    {% if assistant.is_active %}Active{% else %}Pending Approval{% endif %}
                                                </span>
                                                <span class="badge {% if assistant.is_public %}bg-info{% else %}bg-light text-dark border{% endif %}">
                                                    {% if assistant.is_public %}Public{% else %}Private{% endif %}
                                                </span>
                                            </span>
                                        </p>
                                        <!-- Creator information -->
                                        <p class="assistant-creator small text-muted">
                                            <i class="bi bi-person me-1"></i>Created by:
                                            {% if assistant.created_by %}
                                                {% if assistant.created_by == request.user %}
                                                    <span class="fw-bold">You</span>
                                                {% else %}
                                                    {{ assistant.created_by.get_full_name|default:assistant.created_by.username }}
                                                {% endif %}
                                            {% else %}
                                                Unknown
                                            {% endif %}
                                            <span class="ms-2">{{ assistant.created_at|date:"M d, Y" }}</span>
                                        </p>
                                        <div class="rating-display-container mb-2" id="rating-display-{{ assistant.id }}">
                                            {% if assistant.listing %}
                                                {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                                            {% else %}
                                                <div class="star-rating"><span class="rating-count ms-1 small">(No ratings yet)</span></div>
                                            {% endif %}
                                            <span id="rating-msg-{{ assistant.id }}" class="text-success small ms-2" style="display: none;"></span>
                                        </div>
                                        {% if not assistant.is_active %}
                                        <div class="alert alert-warning py-2 mb-2" role="alert">
                                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                            <strong>Pending Approval:</strong> This assistant is waiting for administrator approval before it can be used.
                                        </div>
                                        {% endif %}
                                        <p class="assistant-description">
                                            {{ assistant.description|truncatechars:200 }}
                                        </p>
                                    </div>

                                    {# Column 3: Enhanced Action Buttons #}
                                    <div class="col-md-3 d-flex justify-content-end align-items-start">
                                        <div class="assistant-actions-container" style="display: flex !important; flex-direction: column !important; gap: 0.375rem !important; min-width: 140px !important; background: rgba(255, 255, 255, 0.02) !important; border-radius: 8px !important; padding: 0.5rem !important; border: 1px solid rgba(255, 255, 255, 0.05) !important;">
                                            <!-- Primary Actions Row -->
                                            <div class="assistant-primary-actions" style="display: flex !important; gap: 0.25rem !important; justify-content: flex-end !important; align-items: center !important; padding-bottom: 0.25rem !important; border-bottom: 1px solid rgba(255, 255, 255, 0.08) !important; margin-bottom: 0.25rem !important;">
                                                <!-- Chat button - always visible -->
                                                <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}"
                                                   class="assistant-action-btn btn-primary"
                                                   title="Start Chat"
                                                   style="border-radius: 6px !important; padding: 0.375rem 0.5rem !important; font-size: 0.75rem !important; min-width: 60px !important; max-width: 80px !important; height: 32px !important; display: flex !important; align-items: center !important; justify-content: center !important; background: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important; color: white !important; text-decoration: none !important;">
                                                    <i class="bi bi-chat-dots" style="font-size: 0.875rem !important;"></i>
                                                    <span class="d-none d-xl-inline ms-1" style="font-size: 0.7rem;">Chat</span>
                                                </a>

                                                <!-- Settings button - only visible if user can edit -->
                                                {% load permission_tags %}
                                                {% if request.user|can_change_assistant:assistant %}
                                                <a href="{% url 'assistants:update' company_id=company.id assistant_id=assistant.id %}"
                                                   class="assistant-action-btn btn-outline-secondary"
                                                   title="Settings"
                                                   style="border-radius: 6px !important; padding: 0.25rem !important; min-width: 28px !important; height: 28px !important; display: flex !important; align-items: center !important; justify-content: center !important; background: rgba(113, 128, 150, 0.1) !important; border: 1px solid rgba(113, 128, 150, 0.3) !important; color: #718096 !important; text-decoration: none !important;">
                                                    <i class="bi bi-gear" style="font-size: 0.75rem !important;"></i>
                                                </a>
                                                {% endif %}

                                                <!-- Analytics button - only visible if user can view analytics -->
                                                {% load permission_tags %}
                                                {% check_perm request.user 'assistants.view_assistant_analytics' assistant as can_view_analytics %}
                                                {% if can_view_analytics %}
                                                <a href="{% url 'assistants:analytics' company_id=company.id assistant_id=assistant.id %}"
                                                   class="assistant-action-btn btn-outline-info"
                                                   title="Analytics"
                                                   style="border-radius: 6px !important; padding: 0.25rem !important; min-width: 28px !important; height: 28px !important; display: flex !important; align-items: center !important; justify-content: center !important; background: rgba(56, 178, 172, 0.1) !important; border: 1px solid rgba(56, 178, 172, 0.3) !important; color: #38b2ac !important; text-decoration: none !important;">
                                                    <i class="bi bi-bar-chart" style="font-size: 0.75rem !important;"></i>
                                                </a>
                                                {% endif %}
                                            </div>

                                            <!-- Secondary Actions Row -->
                                            <div class="assistant-secondary-actions" style="display: flex !important; gap: 0.25rem !important; flex-wrap: wrap !important; justify-content: flex-end !important; align-items: center !important;">
                                                <!-- Public chat button - only for public assistants -->
                                                {% if assistant.is_public %}
                                                <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}"
                                                   class="assistant-action-btn btn-outline-success"
                                                   title="Open Public Chat"
                                                   target="_blank"
                                                   style="border-radius: 6px !important; padding: 0.25rem !important; min-width: 28px !important; height: 28px !important; display: flex !important; align-items: center !important; justify-content: center !important; background: rgba(207, 46, 46, 0.1) !important; border: 1px solid rgba(207, 46, 46, 0.3) !important; color: #cf2e2e !important; text-decoration: none !important;">
                                                    <i class="bi bi-box-arrow-up-right" style="font-size: 0.75rem !important;"></i>
                                                </a>
                                                {% endif %}

                                                <!-- Rate button - for authenticated users -->
                                                {% if user.is_authenticated %}
                                                <button type="button"
                                                        class="assistant-action-btn btn-outline-warning rate-assistant-btn"
                                                        title="Rate Assistant"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#ratingModal"
                                                        data-assistant-id="{{ assistant.id }}"
                                                        data-assistant-name="{{ assistant.name|escapejs }}"
                                                        style="border-radius: 6px !important; padding: 0.25rem !important; min-width: 28px !important; height: 28px !important; display: flex !important; align-items: center !important; justify-content: center !important; background: rgba(237, 137, 54, 0.1) !important; border: 1px solid rgba(237, 137, 54, 0.3) !important; color: #ed8936 !important;">
                                                    <i class="bi bi-star" style="font-size: 0.75rem !important;"></i>
                                                </button>
                                                {% endif %}

                                                <!-- Folder assignment - only for users who can edit -->
                                                {% if request.user|can_change_assistant:assistant %}
                                                <div class="assistant-folder-dropdown dropdown">
                                                    <button class="assistant-action-btn btn-outline-secondary dropdown-toggle"
                                                            type="button"
                                                            id="assignFolderDropdown{{ assistant.id }}"
                                                            data-bs-toggle="dropdown"
                                                            aria-expanded="false"
                                                            title="Assign Folder"
                                                            style="border-radius: 6px !important; padding: 0.25rem !important; min-width: 28px !important; height: 28px !important; display: flex !important; align-items: center !important; justify-content: center !important; background: rgba(113, 128, 150, 0.1) !important; border: 1px solid rgba(113, 128, 150, 0.3) !important; color: #718096 !important;">
                                                        <i class="bi bi-folder-symlink" style="font-size: 0.75rem !important;"></i>
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="assignFolderDropdown{{ assistant.id }}">
                                                        <li><a class="dropdown-item assign-folder-action" href="#" data-assistant-id="{{ assistant.id }}" data-folder-id="unassigned">
                                                            <i class="bi bi-box me-2"></i>Unassigned
                                                        </a></li>
                                                        {% if folders %}
                                                            <li><hr class="dropdown-divider"></li>
                                                            {% for folder_option in folders %}
                                                            <li><a class="dropdown-item assign-folder-action" href="#" data-assistant-id="{{ assistant.id }}" data-folder-id="{{ folder_option.id }}">
                                                                <i class="bi bi-folder me-2"></i>{{ folder_option.name }}
                                                            </a></li>
                                                            {% endfor %}
                                                        {% endif %}
                                                    </ul>
                                                </div>
                                                {% endif %}

                                                <!-- Delete button - only for users who can delete -->
                                                {% if request.user|can_delete_assistant:assistant %}
                                                <button type="button"
                                                        class="assistant-action-btn btn-outline-danger js-trigger-delete-assistant-modal"
                                                        title="Delete Assistant"
                                                        data-assistant-id="{{ assistant.id }}"
                                                        data-assistant-name="{{ assistant.name|escapejs }}"
                                                        data-delete-url="{% url 'assistants:delete' company_id=company.id assistant_id=assistant.id %}"
                                                        style="border-radius: 6px !important; padding: 0.25rem !important; min-width: 28px !important; height: 28px !important; display: flex !important; align-items: center !important; justify-content: center !important; background: rgba(245, 101, 101, 0.1) !important; border: 1px solid rgba(245, 101, 101, 0.3) !important; color: #f56565 !important;">
                                                    <i class="bi bi-trash" style="font-size: 0.75rem !important;"></i>
                                                </button>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        {# This case shouldn't happen with the current grouping logic, but good to have #}
                        <p class="text-muted ms-4">No assistants in this folder.</p>
                    {% endif %}
                </div>
            {% endfor %}

        {# Pagination with items per page #}
        {% if page_obj.paginator.count > 0 %}
        <div class="mt-4">
            {% include "pagination_with_items_per_page.html" with page_obj=page_obj %}
        </div>
        {% endif %}

    {% else %}
            <div class="text-center py-5">
                <i class="bi bi-robot h1 text-muted mb-3"></i>
                <p class="text-muted">
                    {% if selected_folder_id %}
                        No assistants found in this folder.
                    {% elif search_query or filter_type %}
                        No assistants match your filters.
                    {% else %}
                        No assistants found for this company.
                    {% endif %}
                </p>
                {% if company.entity_type == 'community' %}
                <a href="{% url 'assistants:create' company_id=company.id %}" class="btn btn-primary mt-2">
                    <i class="bi bi-plus-lg me-1"></i> Create Your First Community Assistant
                </a>
                {% else %}
                <a href="{% url 'assistants:create' company_id=company.id %}" class="btn btn-primary mt-2">
                    <i class="bi bi-plus-lg me-1"></i> Create Your First Assistant
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

{# --- Modals for Folder Actions --- #}
{# Create Folder Modal #}
<div class="modal fade" id="createFolderModal" tabindex="-1" aria-labelledby="createFolderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="createFolderModalLabel">Create New Folder</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="createFolderForm">
        {# {% csrf_token %} No need in form if sent via JS header #}
        <div class="modal-body">
          {# Removed hidden folder_id input as it's not needed for create #}
          <div class="mb-3">
            <label for="createFolderNameInput" class="form-label">Folder Name</label>
            <input type="text" class="form-control" id="createFolderNameInput" name="name" required>
          </div>
          {# Add Order Field #}
          <div class="mb-3">
              <label for="createFolderOrderInput" class="form-label">Display Order</label>
              <input type="number" class="form-control" id="createFolderOrderInput" name="order" placeholder="0" value="0"> {# Default value 0 #}
              <div class="form-text small">Lower numbers appear first.</div>
          </div>
          <div id="createFolderErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary" id="createFolderSubmitBtn">Create Folder</button>
        </div>
      </form>
    </div>
  </div>
</div>

{# Edit Folder Modal #}
<div class="modal fade" id="editFolderModal" tabindex="-1" aria-labelledby="editFolderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editFolderModalLabel">Edit Folder</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="editFolderForm">
         {# {% csrf_token %} No need in form if sent via JS header #}
        <input type="hidden" id="editFolderIdInput" name="folder_id">
        <div class="modal-body">
          <div class="mb-3">
            <label for="editFolderNameInput" class="form-label">Folder Name</label>
            <input type="text" class="form-control" id="editFolderNameInput" name="name" required>
          </div>
          {# Add Order Field #}
          <div class="mb-3">
              <label for="editFolderOrderInput" class="form-label">Display Order</label>
              <input type="number" class="form-control" id="editFolderOrderInput" name="order" placeholder="0" value="0"> {# Default value 0 #}
              <div class="form-text small">Lower numbers appear first.</div>
          </div>
          <div id="editFolderErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary" id="saveFolderEditBtn">Save Changes</button>
        </div>
      </form>
    </div>
  </div>
</div>

{# Delete Folder Confirmation Modal #}
<div class="modal fade" id="deleteFolderModal" tabindex="-1" aria-labelledby="deleteFolderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteFolderModalLabel">Confirm Deletion</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <input type="hidden" id="deleteFolderIdInput">
        <p>Are you sure you want to delete the folder "<strong id="deleteFolderNameSpan"></strong>"?</p>
        <p class="small text-muted">Assistants in this folder will become uncategorized.</p>
        <div id="deleteFolderErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteFolderBtn">Delete Folder</button>
      </div>
    </div>
  </div>
</div>
{# --- End Modals --- #}

{# --- Add Delete Assistant Confirmation Modal --- #}
<div class="modal fade" id="deleteAssistantModal" tabindex="-1" aria-labelledby="deleteAssistantModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteAssistantModalLabel">Confirm Assistant Deletion</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        {# Hidden input to store the actual delete URL #}
        <input type="hidden" id="confirmDeleteAssistantUrlInput">
        <p>Are you sure you want to delete the assistant "<strong id="deleteAssistantNameSpan"></strong>"?</p>
        <p class="small text-danger">This action cannot be undone.</p>
        {# Optional: Add error message display area #}
        <div id="deleteAssistantErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteAssistantBtn">Delete Assistant</button>
      </div>
    </div>
  </div>
</div>
{# --- End Delete Assistant Modal --- #}

{# Rating Modal Structure #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Select your rating for <strong id="modalAssistantName">this assistant</strong>:</p>
        <div class="modal-stars text-center mb-3" style="font-size: 2rem;">
            {% for i_int in "12345" %}
            <button class="modal-star-btn btn btn-link text-secondary p-1" data-rating-value="{{ i_int }}" title="Rate {{ i_int }} star{{ i_int|pluralize }}">
                <i class="bi bi-star"></i>
            </button>
            {% endfor %}
        </div>
        <div id="modalErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
      </div>
    </div>
  </div>
</div>
{# End Rating Modal #}

{% endblock %}

{% block extra_js %}

<script>
// Define the base URL for the rating endpoint
const rateAssistantBaseUrl = "/directory/rate-assistant/";

// Helper function to get CSRF token from the cookie (more reliable for AJAX)
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            // Does this cookie string begin with the name we want?
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
const csrftoken = getCookie('csrftoken'); // Get token once on load

document.addEventListener('DOMContentLoaded', function() {

    // --- Folder Management JS ---
    const createFolderModalElement = document.getElementById('createFolderModal');
    const editFolderModalElement = document.getElementById('editFolderModal');
    const deleteFolderModalElement = document.getElementById('deleteFolderModal');

    // Create Folder
    if (createFolderModalElement) {
        const createFolderForm = createFolderModalElement.querySelector('#createFolderForm');
        const createErrorMsg = createFolderModalElement.querySelector('#createFolderErrorMsg');
        const createSubmitBtn = createFolderModalElement.querySelector('#createFolderSubmitBtn');
        const createFolderNameInput = createFolderModalElement.querySelector('#createFolderNameInput');
        const createFolderOrderInput = createFolderModalElement.querySelector('#createFolderOrderInput'); // Get order input

        createFolderForm.addEventListener('submit', async (event) => {
            event.preventDefault();
            // Use the globally fetched csrftoken
            const folderName = createFolderNameInput.value.trim();
            const folderOrder = createFolderOrderInput.value || '0'; // Get order value, default to 0

            if (!csrftoken) {
                 createErrorMsg.textContent = "CSRF token not found. Please refresh the page.";
                 createErrorMsg.style.display = 'block';
                 return;
            }

            if (!folderName) {
                createErrorMsg.textContent = "Folder name cannot be empty.";
                createErrorMsg.style.display = 'block';
                return;
            }
            createErrorMsg.style.display = 'none';
            createSubmitBtn.disabled = true;
            createSubmitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating...';

            try {
                const response = await fetch("{% url 'assistants:folder_create' company_id=company.id %}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrftoken, // Use fetched token
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    // Send both name and order
                    body: new URLSearchParams({ 'name': folderName, 'order': folderOrder })
                });
                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    window.location.reload(); // Reload to show the new folder
                } else {
                    // Display specific field errors or general message
                    let errorText = data.message || 'Error creating folder.';
                    if (data.errors) {
                        if (data.errors.name) errorText = data.errors.name[0];
                        else if (data.errors.order) errorText = data.errors.order[0];
                        else errorText = Object.values(data.errors).flat().join(' '); // Fallback
                    }
                    createErrorMsg.textContent = errorText;
                    createErrorMsg.style.display = 'block';
                }
            } catch (error) {
                console.error('Error creating folder:', error);
                createErrorMsg.textContent = 'An unexpected error occurred.';
                createErrorMsg.style.display = 'block';
            } finally {
                 createSubmitBtn.disabled = false;
                 createSubmitBtn.innerHTML = 'Create Folder';
            }
        });
    }

    // Edit Folder
    if (editFolderModalElement) {
        const editFolderForm = editFolderModalElement.querySelector('#editFolderForm');
        const editFolderIdInput = editFolderModalElement.querySelector('#editFolderIdInput');
        const editFolderNameInput = editFolderModalElement.querySelector('#editFolderNameInput');
        const editFolderOrderInput = editFolderModalElement.querySelector('#editFolderOrderInput'); // Get order input
        const editErrorMsg = editFolderModalElement.querySelector('#editFolderErrorMsg');
        const saveFolderEditBtn = editFolderModalElement.querySelector('#saveFolderEditBtn');

        document.body.addEventListener('click', (event) => {
            const editBtn = event.target.closest('.edit-folder-btn');
            if (editBtn) {
                event.preventDefault();
                const folderId = editBtn.dataset.folderId;
                const folderName = editBtn.dataset.folderName;
                // Fetch current order (we need to add this data attribute to the button)
                const currentOrder = editBtn.dataset.folderOrder || '0'; // Default to 0 if not set
                editFolderIdInput.value = folderId;
                editFolderNameInput.value = folderName;
                editFolderOrderInput.value = currentOrder; // Set current order in the form
                editErrorMsg.style.display = 'none';
                const editModal = bootstrap.Modal.getInstance(editFolderModalElement) || new bootstrap.Modal(editFolderModalElement);
                editModal.show();
            }
        });

        editFolderForm.addEventListener('submit', async (event) => {
            event.preventDefault();
            const folderId = editFolderIdInput.value;
            const newName = editFolderNameInput.value.trim();
            const newOrder = editFolderOrderInput.value || '0'; // Get order value, default to 0
            // Use the globally fetched csrftoken
            const url = `/assistants/folder/${folderId}/edit/`; // Use dynamic URL

            if (!csrftoken) {
                 editErrorMsg.textContent = "CSRF token not found. Please refresh the page.";
                 editErrorMsg.style.display = 'block';
                 return;
            }

            if (!newName) {
                editErrorMsg.textContent = "Folder name cannot be empty.";
                editErrorMsg.style.display = 'block';
                return;
            }
            editErrorMsg.style.display = 'none';
            saveFolderEditBtn.disabled = true;
            saveFolderEditBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrftoken, // Use fetched token
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({ 'name': newName, 'order': newOrder }) // Send order value
                });
                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    window.location.reload(); // Reload to reflect changes
                } else {
                     // Display specific field errors or general message
                    let errorText = data.message || 'Error updating folder.';
                    if (data.errors) {
                        if (data.errors.name) errorText = data.errors.name[0];
                        else if (data.errors.order) errorText = data.errors.order[0];
                        else errorText = Object.values(data.errors).flat().join(' '); // Fallback
                    }
                    editErrorMsg.textContent = errorText;
                    editErrorMsg.style.display = 'block';
                }
            } catch (error) {
                console.error('Error updating folder:', error);
                editErrorMsg.textContent = 'An unexpected error occurred.';
                editErrorMsg.style.display = 'block';
            } finally {
                saveFolderEditBtn.disabled = false;
                saveFolderEditBtn.innerHTML = 'Save Changes';
            }
        });
    }

    // Delete Folder
    if (deleteFolderModalElement) {
        const deleteFolderIdInput = deleteFolderModalElement.querySelector('#deleteFolderIdInput');
        const deleteFolderNameSpan = deleteFolderModalElement.querySelector('#deleteFolderNameSpan');
        const confirmDeleteBtn = deleteFolderModalElement.querySelector('#confirmDeleteFolderBtn');
        const deleteErrorMsg = deleteFolderModalElement.querySelector('#deleteFolderErrorMsg');

        document.body.addEventListener('click', (event) => {
            const deleteBtn = event.target.closest('.delete-folder-btn');
            if (deleteBtn) {
                event.preventDefault();
                const folderId = deleteBtn.dataset.folderId;
                const folderName = deleteBtn.dataset.folderName;
                deleteFolderIdInput.value = folderId;
                deleteFolderNameSpan.textContent = folderName;
                deleteErrorMsg.style.display = 'none';
                const deleteModal = bootstrap.Modal.getInstance(deleteFolderModalElement) || new bootstrap.Modal(deleteFolderModalElement);
                deleteModal.show();
            }
        });

        confirmDeleteBtn.addEventListener('click', async () => {
            const folderId = deleteFolderIdInput.value;
            // Use the globally fetched csrftoken
            const url = `/assistants/folder/${folderId}/delete/`; // Use dynamic URL

             if (!csrftoken) {
                 deleteErrorMsg.textContent = "CSRF token not found. Please refresh the page.";
                 deleteErrorMsg.style.display = 'block';
                 return;
            }

            confirmDeleteBtn.disabled = true;
            confirmDeleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...';
            deleteErrorMsg.style.display = 'none';

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrftoken, // Use fetched token
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    window.location.reload(); // Reload to reflect deletion
                } else {
                    deleteErrorMsg.textContent = data.message || 'Error deleting folder.';
                    deleteErrorMsg.style.display = 'block';
                }
            } catch (error) {
                console.error('Error deleting folder:', error);
                deleteErrorMsg.textContent = 'An unexpected error occurred.';
                deleteErrorMsg.style.display = 'block';
            } finally {
                confirmDeleteBtn.disabled = false;
                confirmDeleteBtn.innerHTML = 'Delete Folder';
            }
        });
    }

    // --- Assign Folder Action ---
    document.body.addEventListener('click', async (event) => {
        const assignLink = event.target.closest('.assign-folder-action');
        if (assignLink) {
            event.preventDefault();
            const assistantId = assignLink.dataset.assistantId;
            const folderId = assignLink.dataset.folderId; // Could be 'unassigned' or an ID
            // Use the globally fetched csrftoken
            const url = `/assistant/assistant/${assistantId}/assign_folder/`;

             if (!csrftoken) {
                 alert("CSRF token not found. Please refresh the page.");
                 return;
            }

            // Optionally provide visual feedback (e.g., disable dropdown)
            const dropdownButton = assignLink.closest('.dropdown').querySelector('button');
            if(dropdownButton) dropdownButton.disabled = true;

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrftoken, // Use fetched token
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({ 'folder_id': folderId })
                });
                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    // --- Move the assistant card visually ---
                    const assistantCard = document.querySelector(`.list-group-item[data-assistant-id="${assistantId}"]`); // Find the card using data attribute
                    if (assistantCard) {
                        const targetListGroupId = `folder-group-${folderId}`; // Construct target list ID
                        const targetListGroup = document.getElementById(targetListGroupId);

                        if (targetListGroup) {
                            targetListGroup.appendChild(assistantCard); // Move the card
                            // Optional: Add a subtle highlight effect
                            assistantCard.classList.add('bg-light-subtle'); // Example class
                            setTimeout(() => assistantCard.classList.remove('bg-light-subtle'), 1500);
                        } else {
                            // Target folder group doesn't exist visually (e.g., it was empty)
                            // In this case, the item will just disappear from its old group.
                            // A full reload might be better if this happens often, but for now,
                            // we'll just move it if the target exists.
                            console.warn(`Target folder group #${targetListGroupId} not found in DOM.`);
                            // We could potentially remove it from the old group anyway
                            assistantCard.remove();
                        }
                    } else {
                        console.warn(`Assistant card with ID ${assistantId} not found.`);
                    }
                    // --- End visual move ---

                    // Optional: Show a success toast notification instead of console.log
                    // Example: showToast(data.message);

                } else {
                    alert(`Error: ${data.message || 'Could not assign folder.'}`);
                }

            } catch (error) {
                console.error('Error assigning folder:', error);
                alert('An unexpected error occurred while assigning the folder.');
            } finally {
                 if(dropdownButton) dropdownButton.disabled = false; // Re-enable dropdown
            }
        }
    });

    // --- Delete Assistant JS ---
    const deleteAssistantModalElement = document.getElementById('deleteAssistantModal');
    if (deleteAssistantModalElement) {
        const deleteAssistantNameSpan = deleteAssistantModalElement.querySelector('#deleteAssistantNameSpan');
        const confirmDeleteAssistantBtn = deleteAssistantModalElement.querySelector('#confirmDeleteAssistantBtn');
        const deleteAssistantUrlInput = deleteAssistantModalElement.querySelector('#confirmDeleteAssistantUrlInput');
        const deleteAssistantErrorMsg = deleteAssistantModalElement.querySelector('#deleteAssistantErrorMsg');

        // --- Handle Delete Assistant Button Clicks Directly ---
        document.querySelectorAll('.js-trigger-delete-assistant-modal').forEach(button => {
            button.addEventListener('click', function(event) {
                event.preventDefault();  // Stop default action immediately
                event.stopPropagation(); // Stop bubbling immediately

                const deleteButton = this; // Reference the clicked button
                const assistantName = deleteButton.getAttribute('data-assistant-name'); // Keep only one declaration
                const deleteUrl = deleteButton.getAttribute('data-delete-url');

                // Update the modal's content
                deleteAssistantNameSpan.textContent = assistantName;
                deleteAssistantUrlInput.value = deleteUrl;
                deleteAssistantErrorMsg.style.display = 'none';
                confirmDeleteAssistantBtn.disabled = false;
                confirmDeleteAssistantBtn.innerHTML = 'Delete Assistant';

                // Show the modal
                const modalElement = document.getElementById('deleteAssistantModal');
                if (modalElement) {
                    const modalInstance = bootstrap.Modal.getOrCreateInstance(modalElement);
                    modalInstance.show();
                } else {
                    console.error("Delete assistant modal element (#deleteAssistantModal) not found!");
                }
            });
        });

        // --- Handle Create Folder Button Clicks Directly ---
        document.querySelectorAll('.js-trigger-create-folder-modal').forEach(button => {
             button.addEventListener('click', function(event) {
                 event.preventDefault();  // Stop default action immediately
                 event.stopPropagation(); // Stop bubbling immediately

                 const modalElement = document.getElementById('createFolderModal');
                 if (modalElement) {
                     // Reset form fields if needed
                     const form = modalElement.querySelector('#createFolderForm');
                     if (form) form.reset();
                     const errorMsg = modalElement.querySelector('#createFolderErrorMsg');
                     if (errorMsg) errorMsg.style.display = 'none';

                     // Show the modal
                     const modalInstance = bootstrap.Modal.getOrCreateInstance(modalElement);
                     modalInstance.show();
                 } else {
                      console.error("Create folder modal element (#createFolderModal) not found!");
                 }
             });
        });


        // Event listener for the final delete confirmation button click (remains the same)
        confirmDeleteAssistantBtn.addEventListener('click', function () {
            const deleteUrl = deleteAssistantUrlInput.value; // Get the URL from the hidden input
            if (!deleteUrl) {
                console.error("Delete URL not found in modal.");
                deleteAssistantErrorMsg.textContent = 'Could not determine delete action. Please try again.';
                deleteAssistantErrorMsg.style.display = 'block';
                return;
            }

            if (!csrftoken) {
                 deleteAssistantErrorMsg.textContent = "CSRF token not found. Please refresh the page.";
                 deleteAssistantErrorMsg.style.display = 'block';
                 return;
            }

            confirmDeleteAssistantBtn.disabled = true;
            confirmDeleteAssistantBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...';
            deleteAssistantErrorMsg.style.display = 'none';

            // Create a hidden form to submit the POST request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = deleteUrl;
            form.style.display = 'none'; // Hide the form

            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrftoken;
            form.appendChild(csrfInput);

            // Append the form to the body and submit it
            document.body.appendChild(form);
            form.submit();

            // No need for fetch/JSON handling here as the form submission
            // will trigger a page reload on success (due to the redirect in the view)
            // or display an error page if something goes wrong server-side.
            // The modal will close automatically on successful navigation.
        });
    }
    // --- End Delete Assistant JS ---

    // --- Rating Modal Logic ---
    const ratingModalElement = document.getElementById('ratingModal');
    if (ratingModalElement) {
        let ratingModal = bootstrap.Modal.getInstance(ratingModalElement);
        if (!ratingModal) {
            try {
                ratingModal = new bootstrap.Modal(ratingModalElement);
            } catch (e) {
                console.error("Failed to initialize Bootstrap Modal for rating:", e);
                return;
            }
        }

        const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
        const modalAssistantName = ratingModalElement.querySelector('#modalAssistantName');
        const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
        const modalSubmitBtn = ratingModalElement.querySelector('#submitRatingBtn');
        const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
        let currentAssistantId = null;
        let selectedRating = 0;

        // 1. Populate modal when triggered (using Bootstrap event)
        ratingModalElement.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;

            if (button && button.classList.contains('rate-assistant-btn')) {
                currentAssistantId = button.getAttribute('data-assistant-id');
                const assistantName = button.getAttribute('data-assistant-name');

                if (modalTitle) modalTitle.textContent = `Rate ${assistantName || 'Assistant'}`;
                if (modalAssistantName) modalAssistantName.textContent = assistantName || 'this assistant';
                ratingModalElement.dataset.itemId = currentAssistantId;
                ratingModalElement.dataset.itemType = 'assistant';

                selectedRating = 0;
                if (modalErrorMsg) modalErrorMsg.style.display = 'none';
                if (modalSubmitBtn) modalSubmitBtn.disabled = true;

                if (modalStarsContainer) {
                    const stars = modalStarsContainer.querySelectorAll('.modal-star-btn');
                    stars.forEach(star => {
                        star.classList.remove('selected', 'text-warning');
                        star.classList.add('text-secondary');
                        const icon = star.querySelector('i');
                        if (icon) {
                            icon.classList.remove('bi-star-fill');
                            icon.classList.add('bi-star');
                        }
                    });
                }
            } else {
                // Clear if triggered by something else
                ratingModalElement.dataset.itemType = '';
                currentAssistantId = null;
            }
        });

        // 2. Handle star selection
        if (modalStarsContainer) {
            modalStarsContainer.addEventListener('click', function(event) {
                const starButton = event.target.closest('.modal-star-btn');
                if (!starButton) return;

                selectedRating = parseInt(starButton.dataset.ratingValue);
                if (modalSubmitBtn) modalSubmitBtn.disabled = false;
                if (modalErrorMsg) modalErrorMsg.style.display = 'none';

                const stars = modalStarsContainer.querySelectorAll('.modal-star-btn');
                stars.forEach(star => {
                    const starValue = parseInt(star.dataset.ratingValue);
                    const icon = star.querySelector('i');
                    if (icon) {
                        if (starValue <= selectedRating) {
                            star.classList.add('selected', 'text-warning');
                            star.classList.remove('text-secondary');
                            icon.classList.remove('bi-star');
                            icon.classList.add('bi-star-fill');
                        } else {
                            star.classList.remove('selected', 'text-warning');
                            star.classList.add('text-secondary');
                            icon.classList.remove('bi-star-fill');
                            icon.classList.add('bi-star');
                        }
                    }
                });
            });
        } else {
             console.error("Modal stars container not found.");
        }

        // 3. Handle rating submission
        if (modalSubmitBtn) {
            modalSubmitBtn.addEventListener('click', async function() {
                const itemType = ratingModalElement.dataset.itemType;
                const id = ratingModalElement.dataset.itemId;

                if (itemType !== 'assistant' || selectedRating === 0 || !id) {
                     if (modalErrorMsg && itemType === 'assistant') {
                        modalErrorMsg.textContent = 'Please select a rating (1-5 stars).';
                        modalErrorMsg.style.display = 'block';
                     } else if (itemType !== 'assistant') {
                         console.log("Submit clicked, but modal not configured for assistant rating.");
                     }
                    return;
                }

                const csrfTokenLocal = getCsrfToken();
                const url = "/directory/rate-assistant/";

                modalSubmitBtn.disabled = true;
                modalSubmitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
                if (modalErrorMsg) modalErrorMsg.style.display = 'none';

                try {
                    const bodyParams = new URLSearchParams();
                    bodyParams.append('assistant_id', id);
                    bodyParams.append('rating', selectedRating);

                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': csrfTokenLocal,
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: bodyParams
                    });

                    const data = await response.json();

                    if (!response.ok || data.status !== 'success') {
                        throw new Error(data.message || `HTTP error ${response.status}`);
                    }

                    // Clear any error messages before hiding the modal
                    if (modalErrorMsg) {
                        modalErrorMsg.style.display = 'none';
                        modalErrorMsg.textContent = '';
                    }

                    ratingModal.hide();

                    const starsHtml = data.rendered_stars_html;

                    // Look for all possible containers to update (main list and carousel)
                    let containersToUpdate = [
                        document.querySelector(`#rating-display-${id}`),
                        document.getElementById(`rating-display-dup-${id}`)
                    ].filter(Boolean); // Filter out null/undefined

                    const noRatingPlaceholders = [
                        document.getElementById(`no-rating-placeholder-${id}`),
                        document.getElementById(`no-rating-placeholder-dup-${id}`)
                    ].filter(Boolean);

                    // If no containers found but placeholders exist, use those
                    if (containersToUpdate.length === 0 && noRatingPlaceholders.length > 0) {
                        containersToUpdate = noRatingPlaceholders;
                    }

                    let updateSuccess = false;
                    if (containersToUpdate.length > 0 && starsHtml) {
                        // Update all found containers
                        containersToUpdate.forEach(container => {
                            container.outerHTML = starsHtml;
                        });
                        updateSuccess = true;
                        console.log(`[Rating] Updated ${containersToUpdate.length} rating containers for assistant ${id}`);
                    } else {
                        console.error("[Rating] Could not find containers or received empty stars HTML for assistant.");
                    }

                    if (updateSuccess) {
                        const rateButton = document.querySelector(`.rate-assistant-btn[data-assistant-id="${id}"]`);
                        const msgSpan = document.getElementById(`rating-msg-${id}`);
                        if (rateButton) {
                             rateButton.disabled = true;
                             rateButton.innerHTML = '<i class="bi bi-check-lg"></i> Rated';
                        }
                        if (msgSpan) {
                             msgSpan.textContent = 'Thanks!';
                             msgSpan.style.display = 'inline';
                        }
                    }

                } catch (error) {
                    console.error('Error submitting assistant rating via modal:', error);
                     if (modalErrorMsg) {
                        modalErrorMsg.textContent = `Error: ${error.message}`;
                        modalErrorMsg.style.display = 'block';
                     }
                } finally {
                    modalSubmitBtn.disabled = false;
                    modalSubmitBtn.innerHTML = 'Submit Rating';
                    ratingModalElement.dataset.itemType = '';
                    ratingModalElement.dataset.itemId = '';
                    currentAssistantId = null;
                }
            });
        } else {
             console.error("Modal submit button not found.");
        }
    }
    // --- End Rating Modal Logic ---

});
</script>

<!-- Dark mode script for assistant list -->
<script src="{% static 'js/assistant-list-dark-mode.js' %}"></script>

<!-- Modern Button Styling -->
<script src="{% static 'js/modern-assistant-buttons.js' %}"></script>
{% endblock %}