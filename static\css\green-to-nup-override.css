/**
 * Green to NUP Color Override
 * Changes all green buttons and elements to NUP party red color (#cf2e2e)
 */

/* ===== SUCCESS BUTTONS ===== */
.btn-success,
.btn-success:not(:disabled):not(.disabled),
.btn-success.active,
.btn-success:active,
.show > .btn-success.dropdown-toggle {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    box-shadow: 0 2px 4px rgba(207, 46, 46, 0.2) !important;
}

.btn-success:hover,
.btn-success:focus,
.btn-success.focus,
.btn-success:not(:disabled):not(.disabled):active:focus,
.btn-success:not(:disabled):not(.disabled).active:focus,
.show > .btn-success.dropdown-toggle:focus {
    background-color: #b82626 !important;
    border-color: #b82626 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== SUCCESS BACKGROUNDS ===== */
.bg-success {
    background-color: #cf2e2e !important;
}

.text-bg-success {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* ===== SUCCESS TEXT ===== */
.text-success {
    color: #cf2e2e !important;
}

.link-success {
    color: #cf2e2e !important;
}

.link-success:hover,
.link-success:focus {
    color: #b82626 !important;
}

/* ===== SUCCESS BADGES ===== */
.badge.bg-success,
.badge.bg-success.tag-badge,
span.badge.bg-success {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important;
    box-shadow: 0 2px 4px rgba(207, 46, 46, 0.3) !important;
    border: none !important;
    letter-spacing: 0.02em !important;
}

/* ===== SUCCESS ALERTS ===== */
.alert-success {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
    color: #cf2e2e !important;
}

.alert-success .alert-link {
    color: #b82626 !important;
}

/* ===== SUCCESS BORDERS ===== */
.border-success {
    border-color: #cf2e2e !important;
}

/* ===== SUCCESS OUTLINE BUTTONS ===== */
.btn-outline-success {
    color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    background-color: transparent !important;
}

.btn-outline-success:hover,
.btn-outline-success:focus,
.btn-outline-success.focus,
.btn-outline-success:not(:disabled):not(.disabled):active,
.btn-outline-success:not(:disabled):not(.disabled).active,
.show > .btn-outline-success.dropdown-toggle {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== FORM CONTROLS ===== */
.form-control:focus {
    border-color: rgba(207, 46, 46, 0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(207, 46, 46, 0.25) !important;
}

.form-check-input:checked {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
}

/* ===== PROGRESS BARS ===== */
.progress-bar.bg-success {
    background-color: #cf2e2e !important;
}

/* ===== LIST GROUP ITEMS ===== */
.list-group-item-success {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
    color: #cf2e2e !important;
}

.list-group-item-success.list-group-item-action:hover,
.list-group-item-success.list-group-item-action:focus {
    background-color: rgba(207, 46, 46, 0.15) !important;
    color: #cf2e2e !important;
}

/* ===== TABLE VARIANTS ===== */
.table-success {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
}

/* ===== SPECIFIC GREEN ELEMENTS ===== */
/* Override any hardcoded green colors */
[style*="background-color: #28a745"],
[style*="background-color: #198754"],
[style*="background-color: green"],
[style*="background: #28a745"],
[style*="background: #198754"],
[style*="background: green"] {
    background-color: #cf2e2e !important;
    background: #cf2e2e !important;
}

[style*="color: #28a745"],
[style*="color: #198754"],
[style*="color: green"] {
    color: #cf2e2e !important;
}

[style*="border-color: #28a745"],
[style*="border-color: #198754"],
[style*="border-color: green"] {
    border-color: #cf2e2e !important;
}

/* ===== FEATURED BADGES ===== */
.tier-badge.tier-featured,
.badge.featured-badge,
.badge.bg-success.position-absolute,
.featured-badge.badge,
span.featured-badge,
.featured-badge,
.badge.tier-featured,
.tier-featured.badge,
span.tier-featured,
.tier-featured {
    background: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    box-shadow: 
        0 4px 12px rgba(207, 46, 46, 0.4) !important,
        0 2px 6px rgba(207, 46, 46, 0.3) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(207, 46, 46, 0.6) !important;
}

/* ===== ADMIN SUCCESS BUTTONS ===== */
.btn-admin-success {
    background: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(207, 46, 46, 0.4), 0 0 0 1px rgba(207, 46, 46, 0.5) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    font-weight: 700 !important;
    letter-spacing: 0.02em !important;
}

.btn-admin-success:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(207, 46, 46, 0.5), 0 0 0 1px rgba(207, 46, 46, 0.6), 0 0 20px rgba(207, 46, 46, 0.4) !important;
    color: white !important;
}

/* ===== CSS VARIABLES OVERRIDE ===== */
:root {
    --bs-success: #cf2e2e !important;
    --bs-success-rgb: 207, 46, 46 !important;
    --success: #cf2e2e !important;
    --admin-success: #cf2e2e !important;
    --admin-gradient-success: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
}

/* ===== SAVE BUTTONS ===== */
.save-without-folder-btn,
.btn:contains("Save"),
button:contains("Save"),
[class*="save-btn"],
[id*="save-btn"],
[class*="save-button"],
[id*="save-button"] {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.save-without-folder-btn:hover,
.btn:contains("Save"):hover,
button:contains("Save"):hover,
[class*="save-btn"]:hover,
[id*="save-btn"]:hover,
[class*="save-button"]:hover,
[id*="save-button"]:hover {
    background-color: #b82626 !important;
    border-color: #b82626 !important;
    color: #ffffff !important;
}
