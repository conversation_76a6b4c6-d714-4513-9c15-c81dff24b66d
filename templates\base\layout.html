{% load static %}
{% load account_tags %}
{% load guardian_tags %} {# Load guardian tags for permission checks #}
{% load permission_tags %} {# Load custom permission tags #}
{% load rating_tags %} {# Load rating tags for star ratings #}
<!DOCTYPE html>
<html lang="en" style="background-color: #FFFFFF !important;" data-theme="light">
<head>
    <!-- Immediate dark background to prevent white flash -->
    <style>
        html, body, main { background-color: #FFFFFF !important; color: #333333 !important; }
    </style>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <meta name="color-scheme" content="dark">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    {% if request.user.is_authenticated and request.real_user and request.real_user.is_authenticated and request.real_user.id != request.user.id %}
    <meta name="is-impersonating" content="true">
    <meta name="impersonated-user" content="{{ request.user.get_username }}">
    <meta name="real-user" content="{{ request.real_user.get_username }}">
    <script>
        // Add global variables for impersonation detection
        window.realUserUsername = "{{ request.real_user.get_username }}";
        window.currentUsername = "{{ request.user.get_username }}";
        window.isImpersonating = true;

        // Add debug info object
        window.debug_impersonation_info = {
            is_impersonate: true,
            real_user_username: "{{ request.real_user.get_username }}",
            impersonated_user_username: "{{ request.user.get_username }}"
        };
    </script>
    {% endif %}
    <title>{% block title %}NUP - National Unity Platform{% endblock %}</title>

    <!-- Favicon - Professional favicon package for search engines -->
    <link rel="apple-touch-icon" sizes="180x180" href="{% static 'img/favicon_io/apple-touch-icon.png' %}">
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'img/favicon_io/favicon-32x32.png' %}">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'img/favicon_io/favicon-16x16.png' %}">
    <link rel="manifest" href="{% static 'img/favicon_io/site.webmanifest' %}">
    <link rel="icon" type="image/x-icon" href="{% static 'img/favicon_io/favicon.ico' %}">
    <link rel="shortcut icon" href="{% static 'img/favicon_io/favicon.ico' %}">

    <!-- Critical CSS for immediate loading -->
    {% if request.resolver_match.url_name == 'home' %}
    <link href="{% static 'css/homepage-critical.css' %}" rel="stylesheet">
    {% else %}
    <!-- Disabled dark mode CSS for NUP light theme -->
    <!-- <link href="{% static 'css/prevent-white-flash.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/filter-background-fix.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/force-dark-mode.css' %}" rel="stylesheet"> -->
    {% endif %}

    <!-- Early-loading JavaScript for dark background fixes -->
    {% if request.resolver_match.url_name != 'home' %}
    <script src="{% static 'js/error-handler-fix.js' %}"></script>
    <!-- Disabled dark mode scripts for NUP light theme -->
    <!-- <script src="{% static 'js/dark-mode-performance-fix.js' %}"></script> -->
    <!-- <script src="{% static 'js/prevent-white-flash.js' %}"></script> -->
    <!-- <script src="{% static 'js/force-dark-mode.js' %}"></script> -->
    <!-- <script src="{% static 'js/filter-background-fix.js' %}"></script> -->
    <script src="{% static 'js/http-protocol-fix.js' %}"></script>
    <script src="{% static 'js/company-logo-fallback.js' %}"></script>
    <script src="{% static 'js/toast-notification.js' %}"></script>
    {% endif %}

    <!-- Google Fonts for NUP Typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- IMMEDIATE Button Text Force - Maximum Priority -->
    <link href="{% static 'css/button-text-force.css' %}" rel="stylesheet">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- NUP Official Theme CSS - Based on nupuganda.org design system -->
    <link href="{% static 'css/nup-official-theme.css' %}" rel="stylesheet">

    <!-- NUP Theme CSS - Load early for consistent branding -->
    <link href="{% static 'css/nup-theme.css' %}" rel="stylesheet">

    <!-- NUP Light Mode Override - Load last to override all dark mode styles -->
    <link href="{% static 'css/nup-light-mode-override.css' %}" rel="stylesheet">

    <!-- NUP Official Footer CSS -->
    <link href="{% static 'css/nup-official-footer.css' %}" rel="stylesheet">

    <!-- Color Cleanup CSS - Remove blues/purples and fix text visibility -->
    <link href="{% static 'css/color-cleanup.css' %}" rel="stylesheet">

    {% if request.resolver_match.url_name == 'home' %}
    <!-- Use same CSS as other pages for consistent footer -->
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <link href="{% static 'css/global-tinymce.css' %}" rel="stylesheet">
    <link href="{% static 'css/directory-theme.css' %}" rel="stylesheet">
    <link href="{% static 'css/featured-carousel.css' %}" rel="stylesheet">
    <link href="{% static 'css/impersonation.css' %}" rel="stylesheet">
    <link href="{% static 'css/eye-friendly-theme.css' %}" rel="stylesheet">
    <!-- Disabled dark mode CSS for NUP light theme -->
    <!-- <link href="{% static 'css/enhanced-dark-mode.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/theme-transitions.css' %}" rel="stylesheet">
    <link href="{% static 'css/body-background-transition.css' %}" rel="stylesheet">
    <!-- <link href="{% static 'css/theme-toggle-button.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/directory-dark-mode.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/directory-cards-dark.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/white-sections-dark.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/homepage-dark-mode.css' %}?v=2024" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/assistants-list-dark-mode.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/vibrant-blue.css' %}" rel="stylesheet">
    <link href="{% static 'css/community-badge.css' %}" rel="stylesheet">
    <link href="{% static 'css/category-badge.css' %}" rel="stylesheet">
    <link href="{% static 'css/rating-compact.css' %}" rel="stylesheet">
    <link href="{% static 'css/community-buttons.css' %}" rel="stylesheet">
    <link href="{% static 'css/badge-visibility.css' %}" rel="stylesheet">
    <link href="{% static 'css/header-text-enhancement.css' %}" rel="stylesheet">
    <link href="{% static 'css/header-glass-buttons.css' %}" rel="stylesheet">
    <!-- <link href="{% static 'css/dark-mode.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/chat-bubbles-dark-mode.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/chat-transitions.css' %}" rel="stylesheet">
    <link href="{% static 'css/chat-container-glass.css' %}" rel="stylesheet">
    <link href="{% static 'css/message-content-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/chat-scroll-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/remove-purple-space.css' %}" rel="stylesheet">
    <link href="{% static 'css/desktop-mode-restore.css' %}" rel="stylesheet">
    <!-- <link href="{% static 'css/header-icons-dark-mode.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/assistant-buttons-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-responsive.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-navbar.css' %}" rel="stylesheet">
    <link href="{% static 'css/touch-friendly.css' %}" rel="stylesheet">
    <link href="{% static 'css/landscape-mode-optimization.css' %}" rel="stylesheet">
    <link href="{% static 'css/container-3d-glow.css' %}" rel="stylesheet">
    <link href="{% static 'css/floating-buttons-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/modal-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/folder-modal-dark.css' %}" rel="stylesheet">
    <link href="{% static 'css/like-button-enhanced.css' %}" rel="stylesheet">
    <link href="{% static 'css/heart-icon-fix.css' %}" rel="stylesheet">
    <!-- <link href="{% static 'css/dark-mode-heart-fix.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/white-hearts-override.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar-dropdown-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/like-button-text-hide.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar-toggle-dark.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar-toggle-mobile-fix.css' %}" rel="stylesheet">
    <!-- <link href="{% static 'css/company-settings-dark-mode.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/glass-notifications.css' %}" rel="stylesheet">
    <link href="{% static 'css/category-dropdowns.css' %}" rel="stylesheet">
    <link href="{% static 'css/company-logo-fallback.css' %}" rel="stylesheet">
    <link href="{% static 'css/unified-header.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-header-gap-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-menu-gap-killer.css' %}" rel="stylesheet">
    <link href="{% static 'css/fonts.css' %}" rel="stylesheet">
    {% else %}
    <!-- Full CSS for other pages -->
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <link href="{% static 'css/global-tinymce.css' %}" rel="stylesheet">
    <link href="{% static 'css/directory-theme.css' %}" rel="stylesheet">
    <link href="{% static 'css/featured-carousel.css' %}" rel="stylesheet">
    <link href="{% static 'css/impersonation.css' %}" rel="stylesheet">
    <link href="{% static 'css/eye-friendly-theme.css' %}" rel="stylesheet">
    <!-- Disabled duplicate dark mode CSS for NUP light theme -->
    <!-- <link href="{% static 'css/enhanced-dark-mode.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/theme-transitions.css' %}" rel="stylesheet">
    <link href="{% static 'css/body-background-transition.css' %}" rel="stylesheet">
    <!-- <link href="{% static 'css/theme-toggle-button.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/directory-dark-mode.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/directory-cards-dark.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/white-sections-dark.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/homepage-dark-mode.css' %}?v=2024" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/assistants-list-dark-mode.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/vibrant-blue.css' %}" rel="stylesheet">
    <link href="{% static 'css/community-badge.css' %}" rel="stylesheet">
    <link href="{% static 'css/category-badge.css' %}" rel="stylesheet">
    <link href="{% static 'css/rating-compact.css' %}" rel="stylesheet">
    <link href="{% static 'css/community-buttons.css' %}" rel="stylesheet">
    <link href="{% static 'css/badge-visibility.css' %}" rel="stylesheet">
    <link href="{% static 'css/header-text-enhancement.css' %}" rel="stylesheet">
    <link href="{% static 'css/header-glass-buttons.css' %}" rel="stylesheet">
    <!-- <link href="{% static 'css/dark-mode.css' %}" rel="stylesheet"> -->
    <!-- <link href="{% static 'css/chat-bubbles-dark-mode.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/chat-transitions.css' %}" rel="stylesheet">
    <link href="{% static 'css/chat-container-glass.css' %}" rel="stylesheet">
    <link href="{% static 'css/message-content-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/chat-scroll-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/remove-purple-space.css' %}" rel="stylesheet">
    <link href="{% static 'css/desktop-mode-restore.css' %}" rel="stylesheet">
    <!-- <link href="{% static 'css/header-icons-dark-mode.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/assistant-buttons-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-responsive.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-navbar.css' %}" rel="stylesheet">
    <link href="{% static 'css/touch-friendly.css' %}" rel="stylesheet">
    <link href="{% static 'css/landscape-mode-optimization.css' %}" rel="stylesheet">
    <link href="{% static 'css/container-3d-glow.css' %}" rel="stylesheet">
    <link href="{% static 'css/floating-buttons-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/modal-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/folder-modal-dark.css' %}" rel="stylesheet">
    <link href="{% static 'css/like-button-enhanced.css' %}" rel="stylesheet">
    <link href="{% static 'css/heart-icon-fix.css' %}" rel="stylesheet">
    <!-- <link href="{% static 'css/dark-mode-heart-fix.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/white-hearts-override.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar-dropdown-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/like-button-text-hide.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar-toggle-dark.css' %}" rel="stylesheet">
    <link href="{% static 'css/navbar-toggle-mobile-fix.css' %}" rel="stylesheet">
    <!-- <link href="{% static 'css/company-settings-dark-mode.css' %}" rel="stylesheet"> -->
    <link href="{% static 'css/glass-notifications.css' %}" rel="stylesheet">
    <link href="{% static 'css/category-dropdowns.css' %}" rel="stylesheet">
    <link href="{% static 'css/company-logo-fallback.css' %}" rel="stylesheet">
    <link href="{% static 'css/unified-header.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-header-gap-fix.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-menu-gap-killer.css' %}" rel="stylesheet">
    <link href="{% static 'css/fonts.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-footer-enhanced.css' %}" rel="stylesheet">
    {% endif %}
    {% block extra_css %}{% endblock %}

    <!-- Final NUP Light Mode Override - Highest Priority -->
    <link href="{% static 'css/nup-light-mode-override.css' %}" rel="stylesheet">

    <!-- Text Visibility Fix - Ensure all text is readable -->
    <link href="{% static 'css/text-visibility-fix.css' %}" rel="stylesheet">

    <!-- NUP Button Styles - Bold white text with hover effects -->
    <link href="{% static 'css/nup-button-styles.css' %}" rel="stylesheet">

    <!-- Enhanced Card Boundaries - Pronounced 3D effects and shadows -->
    <link href="{% static 'css/enhanced-card-boundaries.css' %}" rel="stylesheet">

    <!-- Compact Tabs - Make tabs more compact to fit in cards -->
    <link href="{% static 'css/compact-tabs.css' %}" rel="stylesheet">

    <!-- Button Text Targeting - Specific classes and IDs for button text -->
    <link href="{% static 'css/button-text-targeting.css' %}" rel="stylesheet">

    <!-- Enhanced Card Visibility - Improved contrast and visibility for all cards -->
    <link href="{% static 'css/enhanced-card-visibility.css' %}" rel="stylesheet">

    <!-- Leadership Cards - Enhanced styling for NUP leadership cards with images -->
    <link href="{% static 'css/leadership-cards.css' %}" rel="stylesheet">

    <!-- Direct style override for heart icons and force white backgrounds -->
    <style>
        /* IMMEDIATE TEXT VISIBILITY FIX */
        * {
            color: #333333 !important;
        }

        /* Exception: Elements that should keep white text */
        .btn-primary, .btn-success, .btn-danger, .btn-warning, .btn-info, .btn-dark,
        .badge:not(.bg-light), .bg-primary, .bg-success, .bg-danger, .bg-warning, .bg-info, .bg-dark,
        [class*="nup-footer"], [id*="nup-footer"],
        [class*="nup-logo"], [id*="nup-logo"],
        [class*="nup-social"], [id*="nup-social"],
        [class*="nup-news"], [id*="nup-news"] {
            color: #ffffff !important;
        }

        /* BOLD WHITE BUTTON TEXT WITH NUP HOVER EFFECTS */
        .btn, button, input[type="button"], input[type="submit"], input[type="reset"] {
            font-weight: 700 !important;
            color: #ffffff !important;
            transition: all 0.3s ease !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
        }

        .btn-primary, .btn:not(.btn-outline-primary):not(.btn-outline-secondary):not(.btn-light) {
            background-color: #cf2e2e !important;
            border-color: #cf2e2e !important;
            color: #ffffff !important;
        }

        .btn-primary:hover, .btn:not(.btn-outline-primary):not(.btn-outline-secondary):not(.btn-light):hover {
            background-color: #252638 !important;
            border-color: #252638 !important;
            color: #ffffff !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
        }

        .btn-secondary {
            background-color: #252638 !important;
            border-color: #252638 !important;
            color: #ffffff !important;
        }

        .btn-secondary:hover {
            background-color: #cf2e2e !important;
            border-color: #cf2e2e !important;
            color: #ffffff !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
        }

        .btn-outline-primary {
            border: 2px solid #cf2e2e !important;
            color: #cf2e2e !important;
            background-color: transparent !important;
        }

        .btn-outline-primary:hover {
            background-color: #cf2e2e !important;
            border-color: #cf2e2e !important;
            color: #ffffff !important;
            transform: translateY(-2px) !important;
        }

        .btn-outline-secondary {
            border: 2px solid #252638 !important;
            color: #252638 !important;
            background-color: transparent !important;
        }

        .btn-outline-secondary:hover {
            background-color: #252638 !important;
            border-color: #252638 !important;
            color: #ffffff !important;
            transform: translateY(-2px) !important;
        }

        /* NUCLEAR OPTION: Force bold white text on ALL buttons */
        .btn, .btn *, button, button *,
        input[type="button"], input[type="submit"], input[type="reset"],
        [class*="btn"], [class*="btn"] *,
        a.btn, a.btn *, span.btn, span.btn * {
            font-weight: 700 !important;
            color: #ffffff !important;
            text-shadow: none !important;
        }

        /* Exception for light and warning buttons */
        .btn-light, .btn-light *, .btn-warning, .btn-warning * {
            color: #333333 !important;
            font-weight: 700 !important;
        }

        /* Override on hover for light/warning */
        .btn-light:hover, .btn-light:hover *,
        .btn-warning:hover, .btn-warning:hover * {
            color: #ffffff !important;
            font-weight: 700 !important;
        }

        /* Maximum specificity override */
        html body .btn, html body .btn *,
        html body button, html body button *,
        html body [class*="btn"], html body [class*="btn"] * {
            font-weight: 700 !important;
            color: #ffffff !important;
        }

        /* Light button exceptions with max specificity */
        html body .btn-light, html body .btn-light *,
        html body .btn-warning, html body .btn-warning * {
            color: #333333 !important;
            font-weight: 700 !important;
        }

        /* NUCLEAR OPTION: Force official NUP white backgrounds on everything EXCEPT footer */
        html, body, main, div, section, article, header, nav,
        .container, .container-fluid, .row, .col {
            background-color: #ffffff !important;  /* Official NUP Neutral White */
            background: #ffffff !important;
            color: #242424 !important;  /* Official NUP Dark Charcoal */
        }

        /* Override any dark mode styles EXCEPT footer */
        .dark-mode, [data-theme="dark"] {
            background-color: #ffffff !important;  /* Official NUP Neutral White */
            background: #ffffff !important;
            color: #242424 !important;  /* Official NUP Dark Charcoal */
        }

        /* Force footer to use new dark blue-gray */
        footer, footer.footer, .footer, .main-footer {
            background-color: #252638 !important;  /* New Dark Blue-Gray */
            background: #252638 !important;
            color: #ffffff !important;  /* White text on dark footer */
            border-top-color: #252638 !important;
        }

        /* Override any footer dark mode styles */
        [data-theme="dark"] footer,
        [data-theme="dark"] footer.footer,
        [data-theme="dark"] .footer,
        [data-theme="dark"] .main-footer {
            background-color: #252638 !important;  /* New Dark Blue-Gray */
            background: #252638 !important;
            color: #ffffff !important;  /* White text on dark footer */
            border-top-color: #252638 !important;
        }

        /* Fix white text visibility on white backgrounds */
        .text-white, .text-light {
            color: #242424 !important;  /* Dark text instead of white */
        }

        /* Ensure proper text contrast on white backgrounds */
        .bg-white .text-white,
        .bg-light .text-white,
        [style*="background-color: white"] .text-white,
        [style*="background-color: #fff"] .text-white,
        [style*="background: white"] .text-white,
        [style*="background: #fff"] .text-white {
            color: #242424 !important;  /* Dark text for visibility */
        }
        [data-theme="dark"] .like-button svg,
        [data-theme="dark"] .btn-like svg,
        [data-theme="dark"] .btn-favorite svg,
        [data-theme="dark"] .favorite-button svg,
        [data-theme="dark"] .like-button:not(.text-danger) svg path,
        [data-theme="dark"] .btn-like:not(.text-danger) svg path,
        [data-theme="dark"] .btn-favorite:not(.text-danger) svg path,
        [data-theme="dark"] .favorite-button:not(.text-danger) svg path {
            fill: #ffffff !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .like-button.text-danger svg,
        [data-theme="dark"] .btn-like.text-danger svg,
        [data-theme="dark"] .btn-favorite.text-danger svg,
        [data-theme="dark"] .favorite-button.text-danger svg,
        [data-theme="dark"] .like-button.text-danger svg path,
        [data-theme="dark"] .btn-like.text-danger svg path,
        [data-theme="dark"] .btn-favorite.text-danger svg path,
        [data-theme="dark"] .favorite-button.text-danger svg path {
            fill: #ff3366 !important;
            color: #ff3366 !important;
        }
    </style>

    <!-- Meta tags -->
    <meta name="description" content="{% block meta_description %}24seven - AI-powered virtual assistant platform{% endblock %}">
    <meta name="theme-color" content="#FFFFFF">

    <!-- Open Graph tags -->
    <meta property="og:title" content="{% block og_title %}{{ self.title }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ self.meta_description }}{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{% static 'img/og-image.svg' %}{% endblock %}">

    <!-- CSRF Token for AJAX requests -->
    <meta name="csrf-token" content="{{ csrf_token }}">

     <!-- REMOVED: TinyMCE CDN Script -->

     <!-- Form Media (for widgets like TinyMCE) -->
     {{ form.media }}
</head>
<body class="light-mode {% block body_class %}{% endblock %}{% if request.user.is_authenticated and request.real_user and request.real_user.is_authenticated and request.real_user.id != request.user.id %} is-impersonating{% endif %}" style="background-color: #ffffff !important; color: #242424 !important; {% block body_style %}{% endblock %}" data-theme="light" data-authenticated="{% if request.user.is_authenticated %}true{% else %}false{% endif %}">
    <!-- Hidden debug elements for impersonation detection -->
    {% if request.user.is_authenticated and request.real_user and request.real_user.is_authenticated and request.real_user.id != request.user.id %}
    <div id="debug-real-user" style="display:none;">{{ request.real_user.username }}</div>
    <div id="debug-current-user" style="display:none;">{{ request.user.username }}</div>
    {% endif %}
    {% block navbar %}
    {% include 'components/unified_header.html' %}
    {% endblock %}

    {% if request.user.is_authenticated and request.real_user and request.real_user.is_authenticated and request.real_user.id != request.user.id %}
    <div class="w-100 text-center py-2" id="impersonation-alert"
         style="background-color: #dc3545; color: white; z-index: 9999; display: flex !important; justify-content: center; align-items: center; position: fixed; top: 0; left: 0; right: 0;">
        <div style="flex-grow: 1; text-align: center;">
            <i class="bi bi-person-badge"></i>
            <strong>IMPERSONATION MODE:</strong> You are currently impersonating <strong>{{ request.user.get_username }}</strong>.
            {% if request.real_user.is_superuser %}(Logged in as a superadmin){% endif %}
        </div>
        <div style="position: absolute; right: 10px;">
            <a href="{% url 'impersonate-stop' %}" class="btn btn-sm btn-outline-light" title="Stop Impersonation">
                <i class="bi bi-x-circle me-1"></i><span class="d-none d-sm-inline">Stop Impersonating</span><span class="d-sm-none">Stop</span>
            </a>
        </div>
    </div>
    <style>
        /* Adjust body and navbar when impersonation is active */
        body.is-impersonating {
            padding-top: 40px !important; /* Height of the impersonation banner */
        }
        body.is-impersonating .navbar.sticky-top {
            top: 40px; /* Position navbar below the impersonation banner */
        }
    </style>
    {% endif %}

    <!-- Impersonation alert is handled in the main script -->

    <!-- Messages -->
    {% if messages %}
        <div class="message-container">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="light-mode" style="background-color: #FFFFFF !important; color: #333333;">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    {% block footer %}
    {% include 'includes/nup-official-footer.html' %}
    {% endblock %}

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>




    <script>
        // CSRF token setup for AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').content;

        // Helper function (can be defined once)
        function getCsrfToken() {
            const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
            if (!csrfInput) {
                const csrfMeta = document.querySelector('meta[name="csrf-token"]');
                if (csrfMeta) return csrfMeta.getAttribute('content');
            }
            return csrfInput ? csrfInput.value : null; // Use the one from meta tag if input not found
        }

        document.addEventListener('DOMContentLoaded', function() {

            // Auto-dismiss alerts (except impersonation alert)
            document.querySelectorAll('.alert:not(.alert-warning[role="alert"])').forEach(alert => {
                setTimeout(() => {
                    const closeButton = alert.querySelector('.btn-close');
                    if (closeButton) closeButton.click();
                }, 5000);
            });

            // Ensure impersonation alert is always visible
            const impersonationAlert = document.getElementById('impersonation-alert');
            if (impersonationAlert) {
                // Add class to body for proper spacing
                document.body.classList.add('is-impersonating');

                // Check visibility once after a short delay
                setTimeout(() => {
                    if (impersonationAlert.style.display === 'none' ||
                        impersonationAlert.style.visibility === 'hidden' ||
                        impersonationAlert.style.opacity === '0') {
                        impersonationAlert.style.display = 'flex';
                        impersonationAlert.style.visibility = 'visible';
                        impersonationAlert.style.opacity = '1';
                    }
                }, 1000);

                // Check visibility periodically but at a much lower frequency
                setInterval(() => {
                    if (impersonationAlert.style.display === 'none' ||
                        impersonationAlert.style.visibility === 'hidden' ||
                        impersonationAlert.style.opacity === '0') {
                        impersonationAlert.style.display = 'flex';
                        impersonationAlert.style.visibility = 'visible';
                        impersonationAlert.style.opacity = '1';
                    }
                }, 10000);
            }

            // Common AJAX error handler (optional)
            window.handleAjaxError = function(error) {
                const message = error.responseJSON?.message || 'An error occurred. Please try again.';
                alert(message);
            };

            // Rating Modal JS removed from here
        });

        // Unified Rating Modal Logic
        const ratingModalElement = document.getElementById('ratingModal');
        if (ratingModalElement) {
            const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
            const submitRatingBtn = ratingModalElement.querySelector('#submitRatingBtn');
            const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
            let ratingAssistantId = null;
            let selectedRating = 0;

            // Set up modal when it's shown
            ratingModalElement.addEventListener('show.bs.modal', function (event) {
                try {
                    const button = event.relatedTarget;
                    if (!button) {
                        return;
                    }

                    ratingAssistantId = button.getAttribute('data-assistant-id');
                    const assistantName = button.getAttribute('data-assistant-name');
                    const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
                    const modalAssistantName = ratingModalElement.querySelector('#modalAssistantName');



                    if (modalTitle) modalTitle.textContent = `Rate ${assistantName}`;
                    if (modalAssistantName) modalAssistantName.textContent = assistantName;

                    // Reset stars
                    const starButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                    starButtons.forEach(btn => {
                        const star = btn.querySelector('i');
                        if (star) star.className = 'bi bi-star'; // Reset to empty star
                        btn.classList.remove('text-warning');
                        btn.classList.add('text-secondary');
                    });

                    // Reset form state
                    selectedRating = 0;
                    if (submitRatingBtn) submitRatingBtn.disabled = true;
                    if (modalErrorMsg) modalErrorMsg.style.display = 'none';
                } catch (error) {
                    // Handle error silently
                }
            });

            // Handle star clicks
            if (modalStarsContainer) {
                modalStarsContainer.addEventListener('click', function (event) {
                    try {
                        const starButton = event.target.closest('.modal-star-btn');
                        if (!starButton) return;

                        const ratingValue = starButton.getAttribute('data-rating-value');
                        if (!ratingValue) {
                            return;
                        }

                        selectedRating = parseInt(ratingValue);


                        if (submitRatingBtn) submitRatingBtn.disabled = false;
                        if (modalErrorMsg) modalErrorMsg.style.display = 'none';

                        // Update star visuals
                        const starButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                        starButtons.forEach(btn => {
                            const btnValue = parseInt(btn.getAttribute('data-rating-value'));
                            const star = btn.querySelector('i');
                            if (star) {
                                if (btnValue <= selectedRating) {
                                    star.className = 'bi bi-star-fill'; // Filled star
                                    btn.classList.remove('text-secondary');
                                    btn.classList.add('text-warning'); // Gold color
                                } else {
                                    star.className = 'bi bi-star'; // Empty star
                                    btn.classList.remove('text-warning');
                                    btn.classList.add('text-secondary'); // Gray color
                                }
                            }
                        });
                    } catch (error) {
                        // Handle error silently
                    }
                });
            }

            // Handle submit button click
            if (submitRatingBtn) {
                submitRatingBtn.addEventListener('click', async function () {
                    try {
                        if (!selectedRating || !ratingAssistantId) {
                            if (modalErrorMsg) {
                                modalErrorMsg.textContent = 'Please select a rating.';
                                modalErrorMsg.style.display = 'block';
                            }
                            return;
                        }

                        // Get CSRF token
                        const csrfToken = getCsrfToken();
                        if (!csrfToken) {
                            if (modalErrorMsg) {
                                modalErrorMsg.textContent = 'CSRF token not found. Please refresh the page.';
                                modalErrorMsg.style.display = 'block';
                            }
                            return;
                        }

                        // Save original button text for restoration
                        const originalButtonText = submitRatingBtn.innerHTML;
                        submitRatingBtn.disabled = true;
                        submitRatingBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';

                        // Construct the URL with the assistant ID
                        const rateUrl = `/directory/rate-assistant/${ratingAssistantId}/`;

                        const formData = new URLSearchParams();
                        formData.append('assistant_id', ratingAssistantId);
                        formData.append('rating', selectedRating);

                        const response = await fetch(rateUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                                'X-CSRFToken': csrfToken,
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: formData
                        });

                        const data = await response.json();

                        if (response.ok && data.status === 'success') {
                            // Update the rating display if available
                            if (data.rendered_stars_html) {
                                const ratingDisplay = document.getElementById(`rating-display-${ratingAssistantId}`);
                                if (ratingDisplay) {
                                    ratingDisplay.innerHTML = data.rendered_stars_html;
                                }
                            }

                            // Close the modal
                            const modalInstance = bootstrap.Modal.getInstance(ratingModalElement);
                            if (modalInstance) modalInstance.hide();

                            // Show success message using toast notification
                            if (typeof showToast === 'function') {
                                showToast('Rating submitted successfully!', 'success');
                            } else {
                                console.log('Rating submitted successfully!');
                            }
                        } else {
                            // Show error message
                            if (modalErrorMsg) {
                                modalErrorMsg.textContent = data.message || 'Error submitting rating.';
                                modalErrorMsg.style.display = 'block';
                            }
                        }
                    } catch (error) {
                        if (modalErrorMsg) {
                            modalErrorMsg.textContent = 'An unexpected error occurred.';
                            modalErrorMsg.style.display = 'block';
                        }
                    } finally {
                        // Restore button state
                        submitRatingBtn.disabled = false;
                        submitRatingBtn.innerHTML = 'Submit Rating';
                    }
                });
            }

            // Handle modal hidden event to ensure proper cleanup
            ratingModalElement.addEventListener('hidden.bs.modal', function () {
                // Remove any lingering backdrop
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }

                // Ensure body classes are cleaned up
                document.body.classList.remove('modal-open');
                document.body.style.removeProperty('overflow');
                document.body.style.removeProperty('padding-right');
            });
        }

    </script>
    {# Rating Modal Structure #}
    <div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p>Select your rating for <strong id="modalAssistantName">this assistant</strong>:</p>
            <div class="modal-stars text-center mb-3" style="font-size: 2rem;"> {# Larger stars in modal #}
                {% for i_int in "12345" %}
                <button class="modal-star-btn btn btn-link text-secondary p-1" data-rating-value="{{ i_int }}" title="Rate {{ i_int }} star{{ i_int|pluralize }}">
                    <i class="bi bi-star"></i>
                </button>
                {% endfor %}
            </div>
            <div id="modalErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
          </div>
        </div>
      </div>
    </div>

    {% block extra_js %}{% endblock %}

    <!-- HTMX Script -->
    <script src="https://unpkg.com/htmx.org@1.9.10" integrity="sha384-D1Kt99CQMDuVetoL1lrYwg5t+9QdHe7NLX/SoJYkXDFfX37iInKRy5xLSi8nO7UC" crossorigin="anonymous"></script>

    <!-- TinyMCE Script -->
    <script src="{% static 'tinymce/tinymce.min.js' %}"></script>
    <script src="{% static 'js/global-tinymce.js' %}"></script>
    <script src="{% static 'js/tinymce-dark-init.js' %}"></script>
    <script src="{% static 'js/simple-tinymce.js' %}"></script>
    <script src="{% static 'js/tinymce-responsive-handler.js' %}"></script>
    <link rel="stylesheet" href="{% static 'css/tinymce-dark-theme.css' %}">
    <!-- <link rel="stylesheet" href="{% static 'css/dark-form-elements.css' %}"> -->
    <link rel="stylesheet" href="{% static 'css/tinymce-content-display.css' %}">
    <link rel="stylesheet" href="{% static 'css/message-content-fix.css' %}">
    <link rel="stylesheet" href="{% static 'css/remove-purple-space.css' %}">
    <link rel="stylesheet" href="{% static 'css/desktop-mode-restore.css' %}">

    <!-- Session Management -->
    <script src="{% static 'js/session-sync.js' %}"></script>

    <!-- Mobile Enhancements -->
    <script src="{% static 'js/mobile-enhancements.js' %}"></script>
    <script src="{% static 'js/touch-interactions.js' %}"></script>
    <script src="{% static 'js/navbar-dropdown-fix.js' %}"></script>
    <script src="{% static 'js/like-button-text-hide.js' %}"></script>
    <script src="{% static 'js/mobile-footer-enhanced.js' %}"></script>

    <!-- Theme Switcher Script -->
    <script src="{% static 'js/enhanced-theme-switcher.js' %}"></script>
    <script src="{% static 'js/theme-toggle-button.js' %}"></script>
    <!-- Disabled dark mode JavaScript for NUP light theme -->
    <!-- <script src="{% static 'js/directory-dark-mode.js' %}"></script> -->
    <!-- <script src="{% static 'js/company-directory-dark-mode.js' %}"></script> -->
    <!-- <script src="{% static 'js/navbar-dark-mode.js' %}"></script> -->
    <!-- <script src="{% static 'js/homepage-dark-mode.js' %}"></script> -->
    <!-- <script src="{% static 'js/assistants-list-dark-mode.js' %}"></script> -->
    <script src="{% static 'js/chat-theme-transition.js' %}"></script>
    <!-- <script src="{% static 'js/facebook-style-dark-mode.js' %}"></script> -->
    <!-- <script src="{% static 'js/company-settings-dark-mode.js' %}"></script> -->

    <!-- Button Fix Scripts -->
    <script src="{% static 'js/button-click-fix.js' %}"></script>
    <script src="{% static 'js/assistant-buttons-fix.js' %}"></script>
    <script src="{% static 'js/menu-buttons-fix.js' %}"></script>
    <script src="{% static 'js/navbar-toggle-fix.js' %}"></script>
    <script src="{% static 'js/performance-fix.js' %}"></script>
    <script src="{% static 'js/chat-scroll-fix.js' %}"></script>

    <!-- Favorites Functionality -->
    <script src="{% static 'js/favorites-functionality.js' %}"></script>
    <script src="{% static 'js/like-button-enhanced.js' %}"></script>
    <script src="{% static 'js/force-white-hearts.js' %}"></script>

    <!-- Modal Fix -->
    <script src="{% static 'js/modal-fix.js' %}"></script>

    <!-- QR Code Regeneration -->
    <script src="{% static 'js/qr-code-regeneration.js' %}"></script>

    <!-- Category Dropdowns -->
    <script src="{% static 'js/category-dropdowns.js' %}"></script>

    <!-- Unified Header -->
    <script src="{% static 'js/unified-header.js' %}"></script>

</body>
</html>
