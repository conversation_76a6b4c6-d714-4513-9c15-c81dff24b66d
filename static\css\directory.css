/* Directory Styles - Common styles for company and assistant listings */

/* Card styling */
.list-group-item {
    transition: all 0.3s ease;
    position: relative;
    overflow: visible;
    border-radius: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    padding: 2rem;
    background-color: rgba(240, 248, 255, 0.9); /* Light blue background */
    min-height: auto; /* Allow height to adapt to content */
    height: auto; /* Allow height to adapt to content */
    box-sizing: border-box; /* Include padding in the height calculation */
    transform: perspective(1000px) rotateX(2deg); /* Subtle 3D effect */
    backdrop-filter: blur(5px); /* Glass effect */
}

.list-group-item:hover {
    transform: perspective(1000px) rotateX(0deg) translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2), 0 15px 15px rgba(0, 0, 0, 0.15);
    border-color: rgba(13, 110, 253, 0.4);
    background-color: rgba(240, 248, 255, 0.95); /* Slightly more opaque on hover */
}

/* Badge styling */
.tier-badge {
    position: absolute;
    top: 0;
    left: 0;
    margin: 0.5rem;
    z-index: 10;
    font-size: 0.7em;
    padding: 0.25em 0.5em;
    border-radius: 0.25rem;
    font-weight: normal;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* Ensure badge doesn't affect layout */
    pointer-events: none;
    max-height: 24px; /* Fixed height for all badges */
}

.tier-gold {
    background-color: #e6a800 !important; /* Darker gold for better contrast */
    color: #212529 !important; /* Dark text for better readability */
    font-weight: 700 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3) !important; /* Light text shadow */
    box-shadow: 0 2px 4px rgba(230, 168, 0, 0.4) !important; /* Enhanced shadow */
}

.tier-silver {
    background-color: #868e96 !important; /* Darker silver for better contrast */
    color: #ffffff !important; /* White text for better readability */
    font-weight: 700 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Dark text shadow */
    box-shadow: 0 2px 4px rgba(134, 142, 150, 0.4) !important; /* Enhanced shadow */
}

.tier-bronze {
    background-color: #a86429 !important; /* Darker bronze for better contrast */
    color: #ffffff !important; /* White text for better readability */
    font-weight: 700 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Dark text shadow */
    box-shadow: 0 2px 4px rgba(168, 100, 41, 0.4) !important; /* Enhanced shadow */
}

.tier-standard {
    background-color: #495057 !important; /* Darker gray for better contrast */
    color: #ffffff !important; /* White text for better readability */
    font-weight: 700 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Dark text shadow */
    box-shadow: 0 2px 4px rgba(73, 80, 87, 0.4) !important; /* Enhanced shadow */
}

.tier-featured {
    background: linear-gradient(135deg, #0a6e31, #0a5727) !important; /* Darker green gradient */
    color: #ffffff !important; /* White text for better readability */
    font-weight: 700 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important; /* Dark text shadow */
    box-shadow: 0 2px 4px rgba(10, 110, 49, 0.4) !important; /* Enhanced shadow */
}

.tag-badge {
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
    font-weight: 700 !important; /* Bolder text for better visibility */
    font-size: 0.8em;
    padding: 0.4em 0.7em;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important; /* Enhanced shadow */
    letter-spacing: 0.02em;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Subtle text shadow for depth */
}

.tag-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important; /* Enhanced shadow on hover */
}

/* Link wrapper styling */
.directory-item-link-wrapper {
    color: inherit;
    transition: all 0.2s ease;
}

.directory-item-link-wrapper:hover {
    text-decoration: none;
    color: inherit;
}

.directory-item-link-wrapper h6 {
    color: #0d6efd;
    transition: color 0.2s ease;
}

.directory-item-link-wrapper:hover h6 {
    color: #0a58ca;
}

/* Modern Featured section styling */
.featured-section {
    margin-bottom: 4rem;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(248, 250, 252, 0.05) 100%);
    border-radius: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.08),
        0 8px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.featured-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.6) 50%, transparent 100%);
}

.featured-section h2 {
    background: linear-gradient(135deg, #1a365d 0%, #2d5aa0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 2rem;
    font-weight: 700;
    font-size: 2rem;
    letter-spacing: -0.02em;
    text-align: center;
    position: relative;
}

.featured-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #3b82f6 0%, #9333ea 100%);
    border-radius: 2px;
}

/* Tier section styling */
.tier-section {
    margin-bottom: 3rem;
}

.tier-section h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.tier-section.gold h3 {
    color: #ffc107;
    border-bottom-color: rgba(255, 193, 7, 0.3);
}

.tier-section.silver h3 {
    color: #6c757d;
    border-bottom-color: rgba(108, 117, 125, 0.3);
}

.tier-section.bronze h3 {
    color: #cd7f32;
    border-bottom-color: rgba(205, 127, 50, 0.3);
}

.tier-section.standard h3 {
    color: #495057;
    border-bottom-color: rgba(73, 80, 87, 0.3);
}

/* Ensure consistent card sizing within tier sections */
.tier-section .list-group,
.company-cards-container {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.tier-section .list-group-item,
.company-cards-container .list-group-item {
    margin-bottom: 0; /* Remove bottom margin as we're using gap */
}

/* Allow cards to adapt to content */
.company-cards-container .list-group-item {
    display: flex;
    flex-direction: column;
    height: auto; /* Allow height to adapt to content */
    min-height: auto; /* Allow height to adapt to content */
    overflow: visible; /* Show all content */
    padding-bottom: 1.5rem; /* Ensure enough padding at the bottom */
}

/* Logo styling */
.logo-container {
    height: 180px; /* Default size for desktop */
    width: 180px; /* Equal to height for perfect square */
    min-height: 180px; /* Default minimum height */
    min-width: 180px; /* Default minimum width */
    max-height: 180px; /* Default maximum height */
    max-width: 180px; /* Default maximum width */
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border-radius: 0.25rem; /* Very slight rounding for square appearance */
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 8px 15px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.08);
    position: relative; /* For absolute positioning of placeholder */
    aspect-ratio: 1/1; /* Enforce 1:1 aspect ratio */
}

/* Company logo images */
.company-logo-img,
.featured-company-logo-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
    display: block;
    max-width: 100%;
    max-height: 100%;
}

/* Default logo styling */
.company-logo-img.default-logo,
.featured-company-logo-img.default-logo {
    opacity: 0.8;
}

/* Hover effects for logo images */
.logo-container:hover .company-logo-img,
.logo-container:hover .featured-company-logo-img {
    transform: scale(1.05);
}

/* Ensure images are visible and properly sized */
.logo-container img {
    max-height: 100% !important;
    max-width: 100% !important;
    height: auto !important;
    width: auto !important;
    object-fit: contain !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force image display for company logos */
.company-logo-img,
.featured-company-logo-img {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    background: transparent !important;
}

/* Fallback image styling */
.fallback-image {
    opacity: 0.9 !important;
    filter: grayscale(10%);
}

/* Ensure logo containers show images */
.logo-container {
    background: white !important;
    overflow: visible !important;
}

.logo-container img {
    position: relative !important;
    z-index: 1 !important;
}

.logo-container:hover {
    transform: scale(1.05) translateY(-10px); /* Maintain the vertical shift */
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25), 0 10px 20px rgba(0, 0, 0, 0.15);
}

.logo-container img {
    max-height: 100%;
    max-width: 100%;
    height: 100%;
    width: 100%;
    object-fit: contain;
    display: block;
    padding: 10px; /* Increased padding to prevent image from touching container edges */
    transition: all 0.3s ease;
}

/* Increase specificity to override potential conflicts */
.logo-container .logo-placeholder {
    color: #0d6efd; /* Brighter blue color */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    overflow: visible; /* Allow icon to overflow slightly if needed */
    padding: 0; /* Remove any padding that might reduce space */
    background-color: rgba(240, 248, 255, 0.5); /* Light blue background */
}

.logo-container .logo-placeholder i {
    font-size: 120px; /* Increased size for better visibility */
    line-height: 1; /* Normal line height */
    display: block; /* Prevent any inline spacing issues */
    transform: scale(1); /* No additional scaling */
    width: 100%; /* Fill the width */
    height: 100%; /* Fill the height */
    text-align: center; /* Center the icon */
    margin: 0; /* Remove any margins */
    padding: 0; /* Remove any padding */
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Add shadow for depth */
}

/* Filter form styling */
.filter-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
    margin-bottom: 2.5rem;
    padding: 2rem;
}

.filter-form .form-control,
.filter-form .form-select {
    border-radius: 0.375rem;
    border: 1px solid rgba(0, 0, 0, 0.15);
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
    border-color: rgba(13, 110, 253, 0.4);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

.filter-form .btn {
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.filter-form .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Rating stars styling */
.star-rating {
    display: inline-flex;
    align-items: center;
    font-size: 0.85em; /* Slightly smaller font size */
}

.star-rating .bi-star-fill {
    color: #f5c518;
    font-size: 0.9em; /* Slightly smaller stars */
}

.star-rating .bi-star {
    color: #ddd;
    font-size: 0.9em; /* Slightly smaller stars */
}

.star-rating .rating-count {
    margin-left: 0.3em; /* Reduced margin */
    font-size: 0.85em; /* Smaller text */
    color: #6c757d;
}

/* Action buttons styling */
.action-btn {
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-btn i {
    margin-right: 0.25rem;
}

/* Carousel styling for featured items */
.featured-carousel .carousel-indicators {
    position: static;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.featured-carousel .carousel-indicators [data-bs-target] {
    background-color: #adb5bd;
    height: 3px;
    width: 30px;
    margin: 0 5px;
    opacity: 0.6;
}

.featured-carousel .carousel-indicators .active {
    background-color: #0d6efd;
    opacity: 1;
}

.featured-carousel .carousel-item {
    padding: 0;
}

/* Featured carousel items (companies, assistants, community assistants) */
.featured-carousel .carousel-item .list-group-item {
    margin-bottom: 0;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.1);
    background-color: rgba(240, 248, 255, 0.9);
    min-height: 230px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1.75rem 1.5rem 1.5rem; /* Increased padding, especially at top for logo */
    border-radius: 1rem;
    transform: perspective(1000px) rotateX(2deg);
    backdrop-filter: blur(5px);
}

/* Featured carousel logo container */
.featured-carousel .logo-container {
    height: 180px; /* Large square size */
    width: 180px; /* Equal to height for perfect square */
    min-height: 180px; /* Ensure minimum height */
    min-width: 180px; /* Ensure minimum width */
    max-height: 180px; /* Ensure maximum height */
    max-width: 180px; /* Ensure maximum width */
    margin: -20px auto 20px; /* Shift up by 20px and add bottom margin */
    border-radius: 0.25rem; /* Very slight rounding for square appearance */
    aspect-ratio: 1/1; /* Enforce 1:1 aspect ratio */
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 8px 15px rgba(0, 0, 0, 0.15);
    background-color: white;
    z-index: 2;
    position: relative;
    transform: translateY(-10px); /* Additional shift up */
}

/* Featured carousel item name */
.featured-carousel h6 {
    font-size: 1.25rem !important;
    font-weight: 700 !important;
    margin-top: 0.5rem;
    text-align: center;
}

/* Filter form styling enhancements */
.filter-form .form-control,
.filter-form .form-select {
    border-radius: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 0.6rem 1rem;
    transition: all 0.2s ease;
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
    border-color: rgba(13, 110, 253, 0.4);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

.filter-form .input-group-text {
    background-color: #f8f9fa;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-right: none;
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
    color: #6c757d;
}

.filter-form .btn {
    border-radius: 0.5rem;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.filter-form .btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.filter-form .btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.filter-form .btn-outline-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Featured carousel styling */
.featured-carousel {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.featured-carousel .carousel-inner {
    border-radius: 1rem;
}

.featured-carousel .carousel-item {
    padding: 1rem;
}

.featured-carousel .carousel-indicators {
    margin-bottom: -1rem;
}

.featured-carousel .carousel-indicators button {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(25, 135, 84, 0.5);
    margin: 0 5px;
}

.featured-carousel .carousel-indicators button.active {
    background-color: rgba(25, 135, 84, 1);
}

/* Featured carousel badges */
.featured-carousel .badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Featured carousel tag badges */
.featured-carousel .tag-badge {
    display: inline-block;
    margin: 0.1rem;
    transition: all 0.2s ease;
}

.featured-carousel .tag-badge:hover {
    transform: scale(1.05);
}

/* Item description styling */
.item-description {
    line-height: 1.7;
    color: #343a40;
    font-size: 1.05rem;
    font-weight: 400;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    letter-spacing: 0.01em;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Company/Assistant name styling */
.list-group-item h6 {
    color: #0d6efd;
    font-size: 1.3rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 0.75rem;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.01em;
}

/* Contact info styling */
.contact-info {
    line-height: 1.6;
    max-height: none;
    overflow: visible;
}

.contact-info li {
    margin-bottom: 0.7rem;
    word-break: break-word;
    display: flex;
    align-items: flex-start;
    font-size: 0.95rem;
    color: #cccccc;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.contact-info li i {
    margin-right: 0.5rem;
    margin-top: 0.2rem;
    color: #0d6efd;
    font-size: 1.1rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.contact-info .contact-text {
    flex: 1;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    font-size: 0.9rem;
    line-height: 1.4;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #cccccc;
}

/* Special handling for website URLs - using a class in the template */
.contact-info .website-url {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    max-width: 100%;
}

/* Special handling for email addresses */
.contact-info .email-text {
    word-break: break-all;
    max-width: 100%;
}

/* Rating styling */
.rating-display-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.star-rating {
    display: inline-flex;
    align-items: center;
    font-size: 1.2em;
}

.star-rating .bi-star-fill {
    color: #ffc107;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
    margin: 0 1px;
}

.star-rating .bi-star {
    color: #dee2e6;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    margin: 0 1px;
}

.star-rating .rating-count {
    font-size: 0.9em;
    margin-left: 0.5em;
    color: #495057;
    font-weight: 500;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Action buttons styling */
.action-btn {
    border-radius: 0.5rem;
    padding: 0.4rem 0.8rem;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.like-button {
    transition: all 0.2s ease;
}

.like-button:hover {
    transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .list-group-item {
        padding: 1.75rem;
    }

    .tier-badge {
        font-size: 0.7em;
        padding: 0.4em 0.6em;
    }

    .featured-section {
        padding: 1.75rem;
    }

    .featured-section h2 {
        font-size: 1.5rem;
    }

    .tier-section h3 {
        font-size: 1.35rem;
    }

    .item-description {
        -webkit-line-clamp: 2;
        line-clamp: 2;
    }
}

@media (max-width: 768px) {
    .list-group-item:hover {
        transform: translateY(-5px);
    }

    .logo-container {
        height: 140px; /* Slightly smaller for mobile but still large */
        width: 140px; /* Equal to height for perfect square */
        min-height: 140px; /* Ensure minimum height on mobile */
        min-width: 140px; /* Ensure minimum width on mobile */
        max-height: 140px; /* Ensure maximum height on mobile */
        max-width: 140px; /* Ensure maximum width on mobile */
        aspect-ratio: 1/1; /* Enforce 1:1 aspect ratio */
    }

    .filter-form {
        padding: 1.5rem;
    }

    .list-group-item {
        padding: 1.5rem;
    }

    .item-description {
        font-size: 0.95rem;
        -webkit-line-clamp: 2;
        line-clamp: 2;
    }

    /* Mobile logo placeholder styling */
    .logo-container .logo-placeholder i {
        font-size: 72px; /* Doubled size from 36px for mobile */
        transform: scale(1); /* No additional scaling */
    }

    /* Adjust column layouts for mobile */
    .directory-item-link-wrapper .col-md-2,
    .directory-item-link-wrapper .col-md-3,
    .directory-item-link-wrapper .col-md-4,
    .directory-item-link-wrapper .col-md-7 {
        padding: 0.5rem;
    }
}

/* Small text styling */
.list-group-item .small,
.list-group-item .text-muted {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #495057 !important;
    letter-spacing: 0.01em;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.list-group-item .text-muted i {
    color: #0d6efd;
    margin-right: 0.25rem;
}

/* General list-group-item styling outside of media queries */
.list-group-item {
    padding: 1.75rem 1.5rem 1.5rem; /* Increased padding, especially at top for logo */
}

/* Responsive styles for directory cards */
/* Tablet styles (between 768px and 992px) */
@media (min-width: 769px) and (max-width: 991.98px) {
    .list-group-item {
        padding: 1.5rem 1.25rem 1.25rem;
        margin-bottom: 1.5rem;
    }

    .logo-container {
        height: 150px;
        width: 150px;
        min-height: 150px;
        min-width: 150px;
        max-height: 150px;
        max-width: 150px;
        margin: -15px auto 15px;
    }

    .logo-container .logo-placeholder i {
        font-size: 100px;
    }

    /* Ensure text doesn't overflow */
    .directory-item-link-wrapper h6 {
        font-size: 1.1rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }

    /* Adjust badge sizes */
    .tag-badge {
        font-size: 0.75em;
        padding: 0.35em 0.6em;
    }

    /* Ensure proper dark mode styling */
    [data-theme="dark"] .list-group-item {
        background-color: #1e1e1e !important;
    }

    [data-theme="dark"] .logo-container {
        background-color: #252525 !important;
    }
}

/* Mobile styles (up to 768px) */
@media (max-width: 768px) {
    .list-group-item {
        padding: 1.25rem 1rem 1rem;
        margin-bottom: 1rem;
        transform: none; /* Remove 3D effect on mobile */
    }

    .list-group-item:hover {
        transform: translateY(-5px); /* Simpler hover effect on mobile */
    }

    .logo-container {
        height: 120px;
        width: 120px;
        min-height: 120px;
        min-width: 120px;
        max-height: 120px;
        max-width: 120px;
        margin: -10px auto 15px;
        transform: translateY(-5px);
    }

    .logo-container:hover {
        transform: scale(1.05) translateY(-5px);
    }

    .logo-container .logo-placeholder i {
        font-size: 80px;
    }

    /* Ensure text is centered and doesn't overflow */
    .directory-item-link-wrapper h6,
    .directory-item-link-wrapper p {
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }

    /* Adjust badge sizes */
    .tag-badge {
        font-size: 0.7em;
        padding: 0.3em 0.5em;
    }

    /* Improve contact info display on mobile */
    .contact-info {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .contact-info li {
        margin-bottom: 0.5rem;
        justify-content: center;
    }

    .contact-info li i {
        font-size: 1rem;
    }

    .contact-info .contact-text {
        font-size: 0.85rem;
        color: #cccccc;
    }

    /* Center align elements in mobile mode */
    .directory-card .col-md-2 {
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    /* Position like, QR code and share buttons below other elements */
    .directory-card .col-md-2:last-child {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    /* Center align rating display */
    .rating-display-container {
        justify-content: center;
    }

    /* Ensure proper dark mode styling */
    [data-theme="dark"] .list-group-item {
        background-color: #1a1a1a !important;
    }

    [data-theme="dark"] .logo-container {
        background-color: #252525 !important;
    }

    [data-theme="dark"] .contact-info {
        border-top-color: rgba(255, 255, 255, 0.1);
    }

    [data-theme="dark"] .directory-card .col-md-2:last-child {
        border-top-color: rgba(255, 255, 255, 0.1);
    }
}

/* Small mobile styles (up to 576px) */
@media (max-width: 576px) {
    .list-group-item {
        padding: 1rem 0.75rem 0.75rem;
        margin-bottom: 0.75rem;
    }

    .logo-container {
        height: 100px;
        width: 100px;
        min-height: 100px;
        min-width: 100px;
        max-height: 100px;
        max-width: 100px;
        margin: -5px auto 10px;
    }

    .logo-container .logo-placeholder i {
        font-size: 60px;
    }

    /* Further reduce text size */
    .directory-item-link-wrapper h6 {
        font-size: 1rem;
    }

    .directory-item-link-wrapper p {
        font-size: 0.85rem;
    }

    /* Adjust badge sizes */
    .tag-badge {
        font-size: 0.65em;
        padding: 0.25em 0.4em;
        margin-right: 0.2rem;
        margin-bottom: 0.2rem;
    }

    /* Further improve contact info display on small mobile */
    .contact-info {
        margin-top: 0.75rem;
        padding-top: 0.75rem;
        text-align: center;
    }

    .contact-info li {
        margin-bottom: 0.4rem;
        justify-content: center;
    }

    .contact-info li i {
        font-size: 0.9rem;
        margin-right: 0.4rem;
    }

    .contact-info .contact-text {
        font-size: 0.8rem;
        line-height: 1.3;
        color: #cccccc;
    }

    /* Adjust column layout for better mobile display */
    .directory-card .row > div {
        margin-bottom: 0.5rem;
    }

    /* Further reduce spacing for action buttons */
    .directory-card .col-md-2:last-child {
        margin-top: 0.75rem;
        padding-top: 0.75rem;
    }

    /* Make buttons more compact */
    .action-btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
    }
}
