/* NUP (National Unity Platform) Theme CSS */

/* ===== NUP OFFICIAL COLOR PALETTE ===== */
/* Based on nupuganda.org design system with new dark accent */
:root {
    /* Core Official Colors - Updated with new dark accent */
    --nup-primary-red: #cf2e2e;        /* NUP Vivid Red - Primary accent */
    --nup-secondary-gold: #f7bd00;     /* NUP Strong Yellow/Gold - Secondary accent */
    --nup-pure-white: #ffffff;         /* Neutral White - Primary background */
    --nup-light-gray: #f7f7f7;         /* Light Gray - Secondary background */
    --nup-dark-charcoal: #242424;      /* Dark Charcoal - Main text */
    --nup-medium-gray: #797979;        /* Medium Gray - Secondary text */
    --nup-dark-accent: #252638;        /* New Dark Blue-Gray - Replaces all blues/purples */

    /* Legacy color mappings for backward compatibility */
    --nup-primary-accent: var(--nup-primary-red);
    --nup-secondary-accent: var(--nup-secondary-gold);
    --nup-primary-background: var(--nup-pure-white);
    --nup-secondary-background: var(--nup-light-gray);
    --nup-text-main: var(--nup-dark-charcoal);
    --nup-text-secondary: var(--nup-medium-gray);

    /* Interactive states */
    --nup-red-hover: #b82626;
    --nup-gold-hover: #e6aa00;
    --nup-dark-accent-hover: #1e1f2e;

    /* Utility colors */
    --nup-border-light: #e5e5e5;
    --nup-shadow: rgba(37, 38, 56, 0.1);
    --nup-shadow-strong: rgba(37, 38, 56, 0.15);

    /* Remove all blue/purple colors */
    --nup-purple: var(--nup-dark-accent);
    --nup-purple-hover: var(--nup-dark-accent-hover);
    --nup-deep-navy: var(--nup-dark-accent);
    --nup-accent-blue: var(--nup-primary-red);
}

/* ===== NUP TYPOGRAPHY ===== */
/* Set base font for the entire document */
html, body {
    font-family: 'Open Sans', 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
    color: var(--nup-dark-gray);
}

/* ===== NUP OFFICIAL TYPOGRAPHY ===== */
/* Following nupuganda.org typography hierarchy */

/* Headings - Merriweather Sans for authority */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6,
.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    font-family: 'Merriweather Sans', 'Montserrat', 'Poppins', sans-serif !important;
    color: var(--nup-dark-charcoal);
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
}

/* Heading sizes */
h1, .h1 { font-size: 2.5rem; }
h2, .h2 { font-size: 2rem; }
h3, .h3 { font-size: 1.75rem; }
h4, .h4 { font-size: 1.5rem; }
h5, .h5 { font-size: 1.25rem; }
h6, .h6 { font-size: 1.1rem; }

/* Body text - DM Sans for readability */
body, p, .text-body, .lead, .card-text, li, span {
    font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    color: var(--nup-dark-charcoal);
    line-height: 1.6;
    font-size: 16px;
}

/* Secondary text for meta information (5% usage) */
.text-meta, .meta-info, .secondary-text, .card-meta {
    color: var(--nup-medium-gray);
    font-size: 0.9rem;
    font-family: 'DM Sans', sans-serif !important;
}

/* Navigation and menu items */
.navbar-brand, .nav-link, .navbar-nav .nav-link {
    font-family: 'Merriweather Sans', 'Montserrat', sans-serif !important;
    color: var(--nup-dark-charcoal);
    font-weight: 600;
    transition: color 0.3s ease;
}

.nav-link:hover, .nav-link.active {
    color: var(--nup-primary-red);
}

/* Button text */
.btn {
    font-family: 'Merriweather Sans', 'Montserrat', sans-serif !important;
    font-weight: 700;
    letter-spacing: 0.5px;
}

/* ===== NUP OFFICIAL BUTTONS ===== */
/* Primary CTA button - Strategic red usage (15% of design) */
.btn-nup-primary, .theme-btn {
    background-color: var(--nup-primary-red);
    border: 2px solid var(--nup-primary-red);
    color: var(--nup-pure-white);
    font-family: 'Merriweather Sans', 'Montserrat', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 12px 24px;
    border-radius: 4px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

.btn-nup-primary:hover, .theme-btn:hover {
    background-color: var(--nup-red-hover);
    border-color: var(--nup-red-hover);
    color: var(--nup-pure-white);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3);
}

/* Secondary button - White with red border */
.btn-nup-secondary {
    background-color: var(--nup-pure-white);
    border: 2px solid var(--nup-primary-red);
    color: var(--nup-primary-red);
    font-family: 'Merriweather Sans', 'Montserrat', sans-serif;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 12px 24px;
    border-radius: 4px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

.btn-nup-secondary:hover {
    background-color: var(--nup-primary-red);
    border-color: var(--nup-primary-red);
    color: var(--nup-pure-white);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3);
}

/* Purple button */
.btn-nup-purple {
    background-color: var(--nup-purple);
    border-color: var(--nup-purple);
    color: var(--nup-pure-white);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    padding: 12px 24px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-nup-purple:hover {
    background-color: var(--nup-purple-hover);
    border-color: var(--nup-purple-hover);
    color: var(--nup-pure-white);
    transform: translateY(-1px);
}

/* Purple secondary button */
.btn-nup-purple-secondary {
    background-color: var(--nup-pure-white);
    border: 1px solid var(--nup-purple);
    color: var(--nup-purple);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    padding: 12px 24px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-nup-purple-secondary:hover {
    background-color: var(--nup-purple);
    border-color: var(--nup-purple);
    color: var(--nup-pure-white);
}

/* ===== NUP OFFICIAL SECTION BACKGROUNDS ===== */
/* 80% Foundation - Clean and uncluttered */

/* Primary white background - Dominant usage (60% of sections) */
.nup-section-white {
    background-color: var(--nup-pure-white);
    color: var(--nup-dark-charcoal);
    padding: 80px 0;
}

/* Red hero section - Strategic red background */
.nup-section-red {
    background-color: var(--nup-primary-red);
    color: var(--nup-pure-white);
    padding: 80px 0;
    position: relative;
}

/* Purple section - Using dark accent instead of purple */
.nup-section-purple {
    background-color: var(--nup-dark-accent);
    color: var(--nup-pure-white);
    padding: 80px 0;
}

/* Light gray for subtle separation (20% of sections) */
.nup-section-light {
    background-color: var(--nup-light-gray);
    color: var(--nup-dark-charcoal);
    padding: 80px 0;
}

/* Hero sections - Clean white with strategic red accents */
.nup-hero-section {
    background-color: var(--nup-pure-white);
    color: var(--nup-dark-charcoal);
    padding: 100px 0;
    position: relative;
}

/* Footer section - Dark grounding with new color */
.nup-section-footer {
    background-color: var(--nup-dark-accent);
    color: var(--nup-pure-white);
    padding: 60px 0 30px 0;
}

/* Red accent section with light background */
.nup-section-red-accent {
    background-color: var(--nup-pure-white);
    border-top: 4px solid var(--nup-primary-red);
    border-bottom: 4px solid var(--nup-primary-red);
    padding: 80px 0;
}

/* Purple accent section with light background */
.nup-section-purple-accent {
    background-color: var(--nup-pure-white);
    border-top: 4px solid var(--nup-purple);
    border-bottom: 4px solid var(--nup-purple);
    padding: 80px 0;
}

/* ===== NUP OFFICIAL CARD SYSTEM ===== */
/* News/Blog cards following nupuganda.org design */
.nup-card, .content-card {
    background-color: var(--nup-pure-white);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--nup-shadow-strong);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--nup-border-light);
}

.nup-card:hover, .content-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px var(--nup-shadow-strong);
}

.nup-card-date, .card-date {
    background-color: var(--nup-primary-red);
    color: var(--nup-pure-white);
    padding: 8px 12px;
    font-weight: 700;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: 'Merriweather Sans', sans-serif;
}

.nup-card-title, .card-title {
    color: var(--nup-dark-charcoal);
    font-family: 'Merriweather Sans', sans-serif;
    font-weight: 700;
    margin: 16px 0 8px 0;
    line-height: 1.3;
}

.nup-card-meta, .card-meta {
    color: var(--nup-medium-gray);
    font-size: 0.9rem;
    margin-bottom: 12px;
}

/* ===== NUP ACCENT ELEMENTS ===== */
/* 15% Primary Accent Usage - Strategic red highlights */
.nup-accent-text, .highlight-primary {
    color: var(--nup-primary-red);
    font-weight: 700;
}

.nup-accent-border, .border-primary {
    border-color: var(--nup-primary-red);
}

.nup-accent-bg, .bg-primary-accent {
    background-color: var(--nup-primary-red);
    color: var(--nup-pure-white);
}

/* 5% Secondary Accent Usage - Decorative yellow/gold */
.nup-gold-accent, .accent-secondary {
    color: var(--nup-secondary-gold);
}

.nup-gold-bg, .bg-secondary-accent {
    background-color: var(--nup-secondary-gold);
    color: var(--nup-dark-charcoal);
}

/* Icon styling with gold accent */
.nup-icon-gold, .icon-accent {
    color: var(--nup-secondary-gold);
    font-size: 1.2rem;
}

/* Red accent bar under headings */
.nup-accent-bar {
    width: 40px;
    height: 3px;
    background-color: var(--nup-primary-red);
    margin: 0 auto 2rem auto;
}

.nup-accent-bar.left {
    margin: 0 0 2rem 0;
}

/* Circular icon containers */
.nup-icon-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--nup-light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 24px;
    color: var(--nup-deep-navy);
    transition: all 0.3s ease;
}

.nup-icon-circle:hover {
    background-color: var(--nup-primary-red);
    color: var(--nup-pure-white);
    transform: scale(1.1);
}

/* ===== NUP CARDS ===== */
.nup-card {
    background-color: var(--nup-pure-white);
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.nup-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.07);
    transform: translateY(-2px);
}

/* Candidate card styling */
.nup-candidate-card {
    background-color: var(--nup-pure-white);
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.nup-candidate-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.nup-candidate-card .card-header {
    background: var(--nup-red-gradient);
    color: var(--nup-pure-white);
    padding: 1rem;
    border-bottom: none;
}

/* Purple variant candidate card */
.nup-candidate-card.purple .card-header {
    background: var(--nup-purple-gradient);
}

/* Card with colored left border */
.nup-card-red-accent {
    border-left: 4px solid var(--nup-primary-red);
}

.nup-card-purple-accent {
    border-left: 4px solid var(--nup-purple);
}

/* ===== NUP GRID SYSTEM ===== */
.nup-grid-3x2 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.nup-grid-4x3 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

/* ===== NUP DECORATIVE ELEMENTS ===== */
/* Brush stroke accent */
.nup-brush-accent {
    position: relative;
}

.nup-brush-accent::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -20px;
    width: 60px;
    height: 20px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 20"><path d="M5,15 Q30,5 55,15" stroke="%23E31B23" stroke-width="3" fill="none" opacity="0.3"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

/* Dotted pattern overlay */
.nup-dots-pattern {
    position: relative;
}

.nup-dots-pattern::after {
    content: '';
    position: absolute;
    top: 0;
    right: -50px;
    width: 100px;
    height: 100%;
    background-image: radial-gradient(circle, var(--nup-light-gray) 2px, transparent 2px);
    background-size: 20px 20px;
    opacity: 0.5;
    pointer-events: none;
}

/* ===== TEXT COLOR UTILITIES ===== */
.text-nup-red {
    color: var(--nup-primary-red) !important;
}

.text-nup-purple {
    color: var(--nup-purple) !important;
}

.text-nup-navy {
    color: var(--nup-deep-navy) !important;
}

/* ===== OVERRIDE DARK BACKGROUNDS ===== */
/* Force white backgrounds and proper text colors */
html, body, main {
    background-color: var(--nup-pure-white) !important;
    color: var(--nup-dark-gray) !important;
}

.dark-mode {
    background-color: var(--nup-pure-white) !important;
    color: var(--nup-dark-gray) !important;
}

/* Override any remaining dark backgrounds */
.bg-dark, .bg-black {
    background-color: var(--nup-pure-white) !important;
    color: var(--nup-dark-gray) !important;
}

/* Ensure cards and containers have white backgrounds */
.card, .container, .row, .col {
    background-color: var(--nup-pure-white) !important;
}

/* Override any black or dark gray backgrounds */
[style*="background-color: #000"],
[style*="background-color: black"],
[style*="background-color: #121212"],
[style*="background: #000"],
[style*="background: black"],
[style*="background: #121212"] {
    background-color: var(--nup-pure-white) !important;
    color: var(--nup-dark-gray) !important;
}

/* ===== NUP OFFICIAL LINK SYSTEM ===== */
a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--nup-primary-red);
    text-decoration: none;
}

/* ===== NUP FORM ELEMENTS ===== */
.form-control, input, textarea, select {
    border: 2px solid var(--nup-border-light);
    border-radius: 4px;
    padding: 12px 16px;
    font-size: 16px;
    color: var(--nup-dark-charcoal);
    background-color: var(--nup-pure-white);
    transition: border-color 0.3s ease;
    font-family: 'DM Sans', sans-serif;
}

.form-control:focus, input:focus, textarea:focus, select:focus {
    border-color: var(--nup-primary-red);
    outline: none;
    box-shadow: 0 0 0 3px rgba(207, 46, 46, 0.1);
}

.form-control::placeholder, input::placeholder, textarea::placeholder {
    color: var(--nup-medium-gray);
}

/* ===== NUP FOOTER SYSTEM ===== */
.nup-footer, .site-footer, .main-footer {
    background-color: var(--nup-dark-accent);
    color: var(--nup-pure-white);
    padding: 60px 0 30px 0;
}

.nup-footer h3, .nup-footer h4, .nup-footer h5,
.main-footer h3, .main-footer h4, .main-footer h5 {
    color: var(--nup-pure-white);
    margin-bottom: 20px;
}

.nup-footer p, .nup-footer li,
.main-footer p, .main-footer li {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

.nup-footer a, .main-footer a {
    color: rgba(255, 255, 255, 0.9);
    transition: color 0.3s ease;
}

.nup-footer a:hover, .main-footer a:hover {
    color: var(--nup-primary-red);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
    .nup-section-white,
    .nup-section-light,
    .nup-section-red,
    .nup-hero-section {
        padding: 40px 0;
    }

    .nup-grid-3x2,
    .nup-grid-4x3 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .btn-nup-primary,
    .btn-nup-secondary {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    h1, .h1 { font-size: 2rem; }
    h2, .h2 { font-size: 1.75rem; }
    h3, .h3 { font-size: 1.5rem; }
}
