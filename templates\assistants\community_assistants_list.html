{% extends 'base/layout.html' %}
{% load static assistant_tags rating_tags %}

{% block title %}Community Assistants{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/directory.css' %}">
<link rel="stylesheet" href="{% static 'css/featured-carousel.css' %}">
<link rel="stylesheet" href="{% static 'css/featured-home-style-carousel.css' %}">
<link rel="stylesheet" href="{% static 'css/home-carousel-mobile-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/enhanced-tier-badges.css' %}">
<!-- <link rel="stylesheet" href="{% static 'css/assistant-list-dark-mode.css' %}"> -->
<!-- <link rel="stylesheet" href="{% static 'css/community-assistants-dark-mode.css' %}"> -->
<link rel="stylesheet" href="{% static 'css/mobile-community-list.css' %}">
<link rel="stylesheet" href="{% static 'css/unified-cards-responsive.css' %}">
<!-- <link rel="stylesheet" href="{% static 'css/unified-cards-dark-mode.css' %}"> -->
<link rel="stylesheet" href="{% static 'css/progressive-icon-placeholders.css' %}">
<style>
    /* Apply directory theme styling */
    .featured-section {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    /* Animation for continuous scrolling */
    @keyframes scroll {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(calc(-50% - 30px)); /* Adjust for margin */
        }
    }

    /* Ensure animation is defined globally as well */
    @-webkit-keyframes scroll {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(calc(-50% - 30px)); /* Adjust for margin */
        }
    }

    /* Fix for carousel hover pause */
    .featured-carousel-items:hover {
        animation-play-state: paused !important;
    }

    /* Compact Community Assistant Carousel Styles */
    .featured-carousel-container {
        width: 100%;
        overflow: hidden;
        position: relative;
        padding: 15px 0 20px 0; /* Minimal padding */
        margin-bottom: 15px;
    }

    .featured-carousel-items {
        display: flex;
        animation: scroll 60s linear infinite;
        width: max-content;
        min-height: auto; /* Remove fixed height */
        animation-play-state: running;
        overflow: visible;
        transition: animation-play-state 0.3s ease;
    }

    .featured-carousel-item {
        flex: 0 0 auto;
        margin: 0 15px; /* Reduced margin */
        display: flex;
        align-items: flex-start;
        justify-content: center;
        position: relative;
    }

    .featured-carousel-item a {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        text-decoration: none;
        width: 180px; /* Compact width */
        color: #333;
        transition: all 0.3s ease;
    }

    .featured-carousel-item a:hover {
        color: #0d6efd;
        text-decoration: none;
    }

    /* Compact item wrapper */
    .featured-item-wrapper {
        position: relative;
        width: 180px;
        min-height: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 15px 10px;
        transition: all 0.3s ease;
        overflow: visible;
    }

    .featured-item-wrapper:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    }

    /* Compact logo container */
    .featured-carousel-item .logo-container {
        height: 60px;
        width: 60px;
        min-height: 60px;
        min-width: 60px;
        max-height: 60px;
        max-width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.08);
        position: relative;
        margin: 0 auto 8px auto;
        z-index: 2;
    }

    .featured-carousel-item .logo-container img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    /* Compact logo placeholder */
    .featured-carousel-item .logo-placeholder {
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background-color: #f8f9fa;
    }

    /* Icon size will be handled by progressive-icon-placeholders.css */

    /* Compact item info */
    .featured-carousel-item .item-info {
        text-align: center;
        width: 100%;
        padding: 5px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .featured-carousel-item .item-info h5 {
        color: #333;
        font-size: 0.85rem;
        font-weight: 600;
        line-height: 1.3;
        margin-bottom: 3px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .featured-carousel-item .item-info p {
        font-size: 0.75rem;
        line-height: 1.2;
        color: #6c757d;
        margin-bottom: 3px;
        font-weight: 400;
    }

    .featured-carousel-item .rating-display-container {
        margin-top: 3px;
    }

    .featured-carousel-item .rating-display-container .star-rating {
        font-size: 0.7rem;
    }

    /* Compact like button */
    .featured-carousel-item .like-button {
        position: absolute;
        top: 5px;
        right: 5px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        transition: all 0.3s ease;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .featured-carousel-item .like-button:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    /* Compact featured section */
    .featured-section {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border: 1px solid #dee2e6;
    }

    .tier-section {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    .list-group-item {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
        overflow: hidden;
        height: auto;
        min-height: 160px;
        margin-bottom: 1rem;
    }

    .list-group-item:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .filter-form {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    /* Logo container styles are now in directory.css */

    .logo-placeholder {
        font-size: 2.5rem;
    }



    .tier-badge {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 0.7em;
        padding: 0.25em 0.5em;
        margin: 0.5rem;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .tier-gold {
        background-color: #ffc107;
        color: #212529;
    }

    .tier-silver {
        background-color: #adb5bd;
        color: #212529;
    }

    .tier-bronze {
        background-color: #cd7f32;
        color: #fff;
    }

    .featured-carousel .carousel-item {
        background-color: #fff;
        border-radius: 0.5rem;
        padding: 1.5rem;
    }

    .carousel-indicators {
        position: relative;
        margin-top: 1rem;
        margin-bottom: 0;
    }

    .carousel-indicators button {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #dee2e6;
    }

    .carousel-indicators button.active {
        background-color: #0d6efd;
    }
</style>
{% endblock %}

{% block head_extra %}
<style>
    /* Rating stars styling */
    .star-rating .stars .bi-star-fill {
        color: gold;
    }
    .star-rating .stars .bi-star {
        color: #ccc;
    }
    .modal-stars .modal-star-btn.active i {
        color: gold !important;
    }

    /* Featured carousel rating styling */
    .featured-carousel-item .rating-display-container {
        margin-top: 5px;
        text-align: center;
    }
    .featured-carousel-item .star-rating {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    .featured-carousel-item .star-rating .stars {
        display: inline-flex !important;
    }
    .featured-carousel-item .star-rating .stars i {
        font-size: 1rem !important;
        margin: 0 1px !important;
        color: gold !important;
    }
    .featured-carousel-item .star-rating .stars i.bi-star {
        color: #ccc !important;
    }

    /* Make stars more clickable */
    .modal-stars .modal-star-btn {
        cursor: pointer;
        padding: 10px !important;
        margin: 0 5px;
        transition: transform 0.2s;
    }
    .modal-stars .modal-star-btn:hover {
        transform: scale(1.2);
    }
    .modal-stars .modal-star-btn i {
        font-size: 1.5em;
    }

    /* Logo container styles are now in directory.css */

    /* Ensure consistent card sizing */
    .list-group-item {
        height: 220px;
        overflow: hidden;
    }
</style>
{% csrf_token %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2 mb-0">Community Assistants</h1>
        {% if request.user.is_authenticated %}
            {% if active_company and active_company.entity_type != 'community' %}
            <a href="{% url 'assistants:create' company_id=active_company.id %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i> Create New Assistant
            </a>
            {% else %}
            <a href="{% url 'accounts:company_create' %}?type=community" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i> Create Community
            </a>
            {% endif %}
        {% else %}
            <a href="{% url 'accounts:login' %}" class="btn btn-primary">
                <i class="bi bi-box-arrow-in-right me-2"></i> Log In to Create
            </a>
        {% endif %}
    </div>



    {# Display Active Filters - Only show filters from search bar #}
    {% if search_query %}
    <div class="alert alert-light border mb-4 mt-2">
        <div class="d-flex align-items-center">
            <i class="bi bi-funnel me-2 text-primary"></i>
            <span class="fw-medium">Filtering by:</span>
            <div class="ms-2">
                {% if search_query %}<span class="badge bg-primary me-1">Search: {{ search_query }}</span>{% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    {# Filter Form #}
    <form method="get" action="/assistant/community/" class="filter-form form-filter-form" id="form-filter-form" data-dark-mode-bg="#121212">
        <h5 class="mb-3"><i class="bi bi-funnel-fill me-2 text-primary"></i>Find Community Assistants</h5>

        <div class="row g-3">
            <div class="col-md-8"> {# Name/Description Filter #}
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" name="q" id="q_name" class="form-control" placeholder="Search by name, description, or company..." value="{{ request.GET.q|default:'' }}">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-center">
                {% if request.GET.q %}
                <a href="/assistant/community/" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-1"></i> Clear Search
                </a>
                {% else %}
                <p class="text-muted mb-0 small"><i class="bi bi-info-circle me-1"></i> Search for community assistants by name, description, or company</p>
                {% endif %}
            </div>
        </div>
    </form>

    {# --- Featured Community Assistants Section (Company List Style Carousel) --- #}
    <div class="featured-section mt-4">
        <h2><i class="bi bi-star-fill me-2"></i>Featured Community Assistants</h2>

        {% if featured_assistants|length > 0 %}
            <!-- Featured Community Assistants Carousel - Company List Style -->
            <div class="company-logo-carousel-container featured-assistants-carousel">
                <div class="company-logo-carousel">
                    {# Display real featured community assistants #}
                    {% for assistant in featured_assistants %}
                        <div class="company-logo-item featured-assistant-item" data-assistant-id="{{ assistant.id }}">
                            <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" title="{{ assistant.name }}" class="text-center">
                                {% with logo_url=assistant.get_logo_url %}
                                    {% if logo_url %}
                                        <div class="logo-container home-carousel-logo">
                                            <img src="{{ logo_url }}"
                                                 alt="{{ assistant.name }} Logo"
                                                 class="company-logo"
                                                 onerror="this.onerror=null; this.src='/static/img/default-assistant-logo.svg';">
                                        </div>
                                    {% else %}
                                        <div class="company-logo-placeholder home-carousel-placeholder">
                                            <i class="bi bi-robot"></i>
                                            <span>{{ assistant.name }}</span>
                                        </div>
                                    {% endif %}
                                {% endwith %}
                                <div class="company-info">
                                    <h5 class="company-name">{{ assistant.name }}</h5>
                                    {% if assistant.description %}
                                        <p class="assistant-description">{{ assistant.description|truncatechars:50|safe }}</p>
                                    {% endif %}
                                    <div class="rating-display-container" id="rating-display-assistant-{{ assistant.id }}">
                                        {% if assistant.listing and assistant.listing.avg_rating and assistant.listing.avg_rating > 0 %}
                                            {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                                        {% else %}
                                            <span class="text-muted fst-italic small">(No ratings yet)</span>
                                        {% endif %}
                                    </div>
                                    {% if assistant.company.name %}
                                        <p class="assistant-count">
                                            <i class="bi bi-building me-1"></i>
                                            {{ assistant.company.name }}
                                        </p>
                                    {% endif %}
                                </div>
                            </a>
                            <!-- Like Button positioned absolutely -->
                            {% if assistant.id %}
                                <button class="like-button btn btn-icon {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                        data-item-id="{{ assistant.id }}"
                                        data-item-type="assistant"
                                        title="{% if assistant.id in saved_assistant_ids %}Unlike{% else %}Like{% endif %}">
                                    <i class="bi bi-heart-fill"></i>
                                </button>
                            {% endif %}
                        </div>
                    {% endfor %}

                    {# Duplicate the real featured community assistants for continuous scrolling effect #}
                    {% if featured_assistants|length >= 3 %}
                        {% for assistant in featured_assistants %}
                            <div class="company-logo-item featured-assistant-item" data-assistant-id="{{ assistant.id }}">
                                <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" title="{{ assistant.name }}" class="text-center">
                                    {% with logo_url=assistant.get_logo_url %}
                                        {% if logo_url %}
                                            <div class="logo-container home-carousel-logo">
                                                <img src="{{ logo_url }}"
                                                     alt="{{ assistant.name }} Logo"
                                                     class="company-logo"
                                                     onerror="this.onerror=null; this.src='/static/img/default-assistant-logo.svg';">
                                            </div>
                                        {% else %}
                                            <div class="company-logo-placeholder home-carousel-placeholder">
                                                <i class="bi bi-robot"></i>
                                                <span>{{ assistant.name }}</span>
                                            </div>
                                        {% endif %}
                                    {% endwith %}
                                    <div class="company-info">
                                        <h5 class="company-name">{{ assistant.name }}</h5>
                                        {% if assistant.description %}
                                            <p class="assistant-description">{{ assistant.description|truncatechars:50|safe }}</p>
                                        {% endif %}
                                        <div class="rating-display-container" id="rating-display-assistant-dup-{{ assistant.id }}">
                                            {% if assistant.listing and assistant.listing.avg_rating and assistant.listing.avg_rating > 0 %}
                                                {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                                            {% else %}
                                                <span class="text-muted fst-italic small">(No ratings yet)</span>
                                            {% endif %}
                                        </div>
                                        {% if assistant.company.name %}
                                            <p class="assistant-count">
                                                <i class="bi bi-building me-1"></i>
                                                {{ assistant.company.name }}
                                            </p>
                                        {% endif %}
                                    </div>
                                </a>
                                <!-- Like Button positioned absolutely -->
                                {% if assistant.id %}
                                    <button class="like-button btn btn-icon {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                            data-item-id="{{ assistant.id }}"
                                            data-item-type="assistant"
                                            title="{% if assistant.id in saved_assistant_ids %}Unlike{% else %}Like{% endif %}">
                                        <i class="bi bi-heart-fill"></i>
                                    </button>
                                {% endif %}
                            </div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                No featured community assistants yet. When community assistants are marked as featured, they will appear here.
            </div>
        {% endif %}
    </div>
    {# --- End Featured Community Assistants Section --- #}

    {# Main Assistant List Section - Grouped by Tier #}
    <div class="mt-5">
        <h2 class="h3 mb-4 fw-bold">
            <i class="bi bi-layers-fill me-2 text-primary"></i>Community Assistants by Tier
        </h2>

    {# Gold Tier Assistants #}
    {% with gold_assistants=assistants|filter:"tier:Gold"|dictsortreversed:"created_at" %}
    {% if gold_assistants %}
    <div class="tier-section gold">
        <h3><i class="bi bi-trophy-fill me-2 text-warning"></i>Gold Tier</h3>
        <div class="list-group company-cards-container">
            {% for assistant in gold_assistants %}
                <div class="list-group-item position-relative directory-card" data-assistant-id="{{ assistant.id }}">
                    <span class="badge tier-badge tier-gold"><i class="bi bi-trophy-fill me-1"></i>Gold</span>
                    {% if assistant.is_featured %}
                    <span class="badge featured-badge"><i class="bi bi-star-fill me-1"></i>Featured</span>
                    {% endif %}
                    <div class="row g-3 pt-3" style="height: 100%;"> <!-- Added consistent styling -->
                        <!-- Link wrapper for most of the content -->
                        <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="directory-item-link-wrapper col-md-10 row g-3 me-0 text-decoration-none text-body">
                            <!-- Column 1: Logo -->
                            <div class="col-md-2 d-flex justify-content-center align-items-start" style="padding-left: 20px;">
                                <div class="logo-container" style="margin-bottom: 15px;">
                                    {% with logo_url=assistant.get_logo_url %}
                                        {% if logo_url %}
                                            <img src="{{ logo_url }}" alt="{{ assistant.name }} logo">
                                        {% else %}
                                            <i class="bi bi-people-fill logo-placeholder text-primary"></i>
                                        {% endif %}
                                    {% endwith %}
                                </div>
                            </div>

                            <!-- Column 2: Name and Company -->
                            <div class="col-md-3 d-flex flex-column justify-content-center" style="padding-left: 40px; margin-top: 10px;">
                                <h6 class="mb-1 fs-6 fw-semibold">{{ assistant.name }}</h6>
                                <p class="mb-1 text-muted small">
                                    <i class="bi bi-building me-1"></i>{{ assistant.company.name }}
                                </p>
                                <div class="mb-2">
                                    <span class="badge bg-secondary tag-badge community-badge">Community</span>
                                </div>
                            </div>

                            <!-- Column 3: Description -->
                            <div class="col-md-7">
                                <p class="mb-2 small">
                                    {% if assistant.description %}
                                        {{ assistant.description|safe|truncatewords_html:30 }}
                                    {% else %}
                                        A community assistant for {{ assistant.company.name }}.
                                    {% endif %}
                                </p>
                            </div>
                        </a>

                        <!-- Column 4: Rating, Like & Actions -->
                        <div class="col-md-2 d-flex flex-column align-items-end justify-content-center">
                            <!-- Rating Display -->
                            <div class="rating-display-container mb-2 w-100 d-flex justify-content-end" id="rating-display-{{ assistant.id }}">
                                {% if assistant.listing and assistant.listing.avg_rating and assistant.listing.avg_rating > 0 %}
                                    {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                                {% else %}
                                    <span class="small text-muted fst-italic">(No ratings yet)</span>
                                {% endif %}
                                <span id="rating-msg-{{ assistant.id }}" class="text-success small ms-2" style="display: none;"></span>
                            </div>

                            <!-- Like Button -->
                            {% if user.is_authenticated %}
                            <div class="w-100 d-flex justify-content-end mb-2">
                                <button
                                    class="like-button btn btn-sm p-1 {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                    data-item-id="{{ assistant.id }}"
                                    data-item-type="assistant"
                                    title="{% if assistant.id in saved_assistant_ids %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                                    style="background: none; border: none; line-height: 1;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314z"/>
                                    </svg>
                                </button>
                            </div>
                            {% endif %}

                            <!-- Chat Button -->
                            <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="btn btn-outline-primary btn-sm mb-2 w-100">
                                <i class="bi bi-chat-dots me-1"></i> Chat Now
                            </a>

                            <!-- Rate Button -->
                            {% if user.is_authenticated %}
                            <button type="button"
                                    class="btn btn-outline-secondary btn-sm rate-assistant-btn w-100"
                                    data-bs-toggle="modal"
                                    data-bs-target="#ratingModal"
                                    data-assistant-id="{{ assistant.id }}"
                                    data-assistant-name="{{ assistant.name|escapejs }}">
                                <i class="bi bi-star me-1"></i> Rate
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    {% endwith %}

    {# Silver Tier Assistants #}
    {% with silver_assistants=assistants|filter:"tier:Silver"|dictsortreversed:"created_at" %}
    {% if silver_assistants %}
    <div class="tier-section silver">
        <h3><i class="bi bi-award-fill me-2 text-secondary"></i>Silver Tier</h3>
        <div class="list-group company-cards-container">
            {% for assistant in silver_assistants %}
                <div class="list-group-item position-relative directory-card" data-assistant-id="{{ assistant.id }}">
                    <span class="badge tier-badge tier-silver"><i class="bi bi-award-fill me-1"></i>Silver</span>
                    {% if assistant.is_featured %}
                    <span class="badge featured-badge"><i class="bi bi-star-fill me-1"></i>Featured</span>
                    {% endif %}
                    <div class="row g-3 pt-3" style="height: 100%;"> <!-- Added consistent styling -->
                        <!-- Link wrapper for most of the content -->
                        <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="directory-item-link-wrapper col-md-10 row g-3 me-0 text-decoration-none text-body">
                            <!-- Column 1: Logo -->
                            <div class="col-md-2 d-flex justify-content-center align-items-start" style="padding-left: 20px;">
                                <div class="logo-container" style="margin-bottom: 15px;">
                                    {% with logo_url=assistant.get_logo_url %}
                                        {% if logo_url %}
                                            <img src="{{ logo_url }}" alt="{{ assistant.name }} logo">
                                        {% else %}
                                            <i class="bi bi-people-fill logo-placeholder text-primary"></i>
                                        {% endif %}
                                    {% endwith %}
                                </div>
                            </div>

                            <!-- Column 2: Name and Company -->
                            <div class="col-md-3 d-flex flex-column justify-content-center" style="padding-left: 40px; margin-top: 10px;">
                                <h6 class="mb-1 fs-6 fw-semibold">{{ assistant.name }}</h6>
                                <p class="mb-1 text-muted small">
                                    <i class="bi bi-building me-1"></i>{{ assistant.company.name }}
                                </p>
                                <div class="mb-2">
                                    <span class="badge bg-secondary tag-badge community-badge">Community</span>
                                </div>
                            </div>

                            <!-- Column 3: Description -->
                            <div class="col-md-7">
                                <p class="mb-2 small">
                                    {% if assistant.description %}
                                        {{ assistant.description|safe|truncatewords_html:30 }}
                                    {% else %}
                                        A community assistant for {{ assistant.company.name }}.
                                    {% endif %}
                                </p>
                            </div>
                        </a>

                        <!-- Column 4: Rating, Like & Actions -->
                        <div class="col-md-2 d-flex flex-column align-items-end justify-content-center">
                            <!-- Rating Display -->
                            <div class="rating-display-container mb-2 w-100 d-flex justify-content-end" id="rating-display-{{ assistant.id }}">
                                {% if assistant.listing and assistant.listing.avg_rating and assistant.listing.avg_rating > 0 %}
                                    {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                                {% else %}
                                    <span class="small text-muted fst-italic">(No ratings yet)</span>
                                {% endif %}
                                <span id="rating-msg-{{ assistant.id }}" class="text-success small ms-2" style="display: none;"></span>
                            </div>

                            <!-- Like Button -->
                            {% if user.is_authenticated %}
                            <div class="w-100 d-flex justify-content-end mb-2">
                                <button
                                    class="like-button btn btn-sm p-1 {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                    data-item-id="{{ assistant.id }}"
                                    data-item-type="assistant"
                                    title="{% if assistant.id in saved_assistant_ids %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                                    style="background: none; border: none; line-height: 1;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314z"/>
                                    </svg>
                                </button>
                            </div>
                            {% endif %}

                            <!-- Chat Button -->
                            <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="btn btn-outline-primary btn-sm mb-2 w-100">
                                <i class="bi bi-chat-dots me-1"></i> Chat Now
                            </a>

                            <!-- Rate Button -->
                            {% if user.is_authenticated %}
                            <button type="button"
                                    class="btn btn-outline-secondary btn-sm rate-assistant-btn w-100"
                                    data-bs-toggle="modal"
                                    data-bs-target="#ratingModal"
                                    data-assistant-id="{{ assistant.id }}"
                                    data-assistant-name="{{ assistant.name|escapejs }}">
                                <i class="bi bi-star me-1"></i> Rate
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    {% endwith %}

    {# Bronze Tier Assistants #}
    {% with bronze_assistants=assistants|filter:"tier:Bronze"|dictsortreversed:"created_at" %}
    {% if bronze_assistants %}
    <div class="tier-section bronze">
        <h3><i class="bi bi-award me-2" style="color: #cd7f32;"></i>Bronze Tier</h3>
        <div class="list-group company-cards-container">
            {% for assistant in bronze_assistants %}
                <div class="list-group-item position-relative directory-card" data-assistant-id="{{ assistant.id }}">
                    <span class="badge tier-badge tier-bronze"><i class="bi bi-award me-1"></i>Bronze</span>
                    {% if assistant.is_featured %}
                    <span class="badge featured-badge"><i class="bi bi-star-fill me-1"></i>Featured</span>
                    {% endif %}
                    <div class="row g-3 pt-3" style="height: 100%;"> <!-- Added consistent styling -->
                        <!-- Link wrapper for most of the content -->
                        <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="directory-item-link-wrapper col-md-10 row g-3 me-0 text-decoration-none text-body">
                            <!-- Column 1: Logo -->
                            <div class="col-md-2 d-flex justify-content-center align-items-start" style="padding-left: 20px;">
                                <div class="logo-container" style="margin-bottom: 15px;">
                                    {% with logo_url=assistant.get_logo_url %}
                                        {% if logo_url %}
                                            <img src="{{ logo_url }}" alt="{{ assistant.name }} logo">
                                        {% else %}
                                            <i class="bi bi-people-fill logo-placeholder text-primary"></i>
                                        {% endif %}
                                    {% endwith %}
                                </div>
                            </div>

                            <!-- Column 2: Name and Company -->
                            <div class="col-md-3 d-flex flex-column justify-content-center" style="padding-left: 40px; margin-top: 10px;">
                                <h6 class="mb-1 fs-6 fw-semibold">{{ assistant.name }}</h6>
                                <p class="mb-1 text-muted small">
                                    <i class="bi bi-building me-1"></i>{{ assistant.company.name }}
                                </p>
                                <div class="mb-2">
                                    <span class="badge bg-secondary tag-badge community-badge">Community</span>
                                </div>
                            </div>

                            <!-- Column 3: Description -->
                            <div class="col-md-7">
                                <p class="mb-2 small">
                                    {% if assistant.description %}
                                        {{ assistant.description|safe|truncatewords_html:30 }}
                                    {% else %}
                                        A community assistant for {{ assistant.company.name }}.
                                    {% endif %}
                                </p>
                            </div>
                        </a>

                        <!-- Column 4: Rating, Like & Actions -->
                        <div class="col-md-2 d-flex flex-column align-items-end justify-content-center">
                            <!-- Rating Display -->
                            <div class="rating-display-container mb-2 w-100 d-flex justify-content-end" id="rating-display-{{ assistant.id }}">
                                {% if assistant.listing and assistant.listing.avg_rating and assistant.listing.avg_rating > 0 %}
                                    {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                                {% else %}
                                    <span class="small text-muted fst-italic">(No ratings yet)</span>
                                {% endif %}
                                <span id="rating-msg-{{ assistant.id }}" class="text-success small ms-2" style="display: none;"></span>
                            </div>

                            <!-- Like Button -->
                            {% if user.is_authenticated %}
                            <div class="w-100 d-flex justify-content-end mb-2">
                                <button
                                    class="like-button btn btn-sm p-1 {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                    data-item-id="{{ assistant.id }}"
                                    data-item-type="assistant"
                                    title="{% if assistant.id in saved_assistant_ids %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                                    style="background: none; border: none; line-height: 1;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314z"/>
                                    </svg>
                                </button>
                            </div>
                            {% endif %}

                            <!-- Chat Button -->
                            <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="btn btn-outline-primary btn-sm mb-2 w-100">
                                <i class="bi bi-chat-dots me-1"></i> Chat Now
                            </a>

                            <!-- Rate Button -->
                            {% if user.is_authenticated %}
                            <button type="button"
                                    class="btn btn-outline-secondary btn-sm rate-assistant-btn w-100"
                                    data-bs-toggle="modal"
                                    data-bs-target="#ratingModal"
                                    data-assistant-id="{{ assistant.id }}"
                                    data-assistant-name="{{ assistant.name|escapejs }}">
                                <i class="bi bi-star me-1"></i> Rate
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    {% endwith %}

    {# Standard Tier Assistants #}
    {% with standard_assistants=assistants|filter:"tier:Standard"|dictsortreversed:"created_at" %}
    {% if standard_assistants and not hide_standard_tier_community_assistants %}
    <div class="tier-section standard">
        <h3><i class="bi bi-person-badge me-2 text-muted"></i>Standard Tier</h3>
        <div class="list-group company-cards-container">
            {% for assistant in standard_assistants %}
                <div class="list-group-item position-relative directory-card" data-assistant-id="{{ assistant.id }}">
                    <!-- Standard tier badge removed as it's the default tier -->
                    {% if assistant.is_featured %}
                    <span class="badge featured-badge"><i class="bi bi-star-fill me-1"></i>Featured</span>
                    {% endif %}
                    <div class="row g-3 pt-3" style="height: 100%;"> <!-- Added consistent styling -->
                        <!-- Link wrapper for most of the content -->
                        <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="directory-item-link-wrapper col-md-10 row g-3 me-0 text-decoration-none text-body">
                            <!-- Column 1: Logo -->
                            <div class="col-md-2 d-flex justify-content-center align-items-start" style="padding-left: 20px;">
                                <div class="logo-container" style="margin-bottom: 15px;">
                                    {% with logo_url=assistant.get_logo_url %}
                                        {% if logo_url %}
                                            <img src="{{ logo_url }}" alt="{{ assistant.name }} logo">
                                        {% else %}
                                            <i class="bi bi-people-fill logo-placeholder text-primary"></i>
                                        {% endif %}
                                    {% endwith %}
                                </div>
                            </div>

                            <!-- Column 2: Name and Company -->
                            <div class="col-md-3 d-flex flex-column justify-content-center" style="padding-left: 40px; margin-top: 10px;">
                                <h6 class="mb-1 fs-6 fw-semibold">{{ assistant.name }}</h6>
                                <p class="mb-1 text-muted small">
                                    <i class="bi bi-building me-1"></i>{{ assistant.company.name }}
                                </p>
                                <div class="mb-2">
                                    <span class="badge bg-secondary tag-badge community-badge">Community</span>
                                </div>
                            </div>

                            <!-- Column 3: Description -->
                            <div class="col-md-7">
                                <p class="mb-2 small">
                                    {% if assistant.description %}
                                        {{ assistant.description|safe|truncatewords_html:30 }}
                                    {% else %}
                                        A community assistant for {{ assistant.company.name }}.
                                    {% endif %}
                                </p>
                            </div>
                        </a>

                        <!-- Column 4: Rating, Like & Actions -->
                        <div class="col-md-2 d-flex flex-column align-items-end justify-content-center">
                            <!-- Rating Display -->
                            <div class="rating-display-container mb-2 w-100 d-flex justify-content-end" id="rating-display-{{ assistant.id }}">
                                {% if assistant.listing and assistant.listing.avg_rating and assistant.listing.avg_rating > 0 %}
                                    {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                                {% else %}
                                    <span class="small text-muted fst-italic">(No ratings yet)</span>
                                {% endif %}
                                <span id="rating-msg-{{ assistant.id }}" class="text-success small ms-2" style="display: none;"></span>
                            </div>

                            <!-- Like Button -->
                            {% if user.is_authenticated %}
                            <div class="w-100 d-flex justify-content-end mb-2">
                                <button
                                    class="like-button btn btn-sm p-1 {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                    data-item-id="{{ assistant.id }}"
                                    data-item-type="assistant"
                                    title="{% if assistant.id in saved_assistant_ids %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                                    style="background: none; border: none; line-height: 1;">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314z"/>
                                    </svg>
                                </button>
                            </div>
                            {% endif %}

                            <!-- Chat Button -->
                            <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="btn btn-outline-primary btn-sm mb-2 w-100">
                                <i class="bi bi-chat-dots me-1"></i> Chat Now
                            </a>

                            <!-- Rate Button -->
                            {% if user.is_authenticated %}
                            <button type="button"
                                    class="btn btn-outline-secondary btn-sm rate-assistant-btn w-100"
                                    data-bs-toggle="modal"
                                    data-bs-target="#ratingModal"
                                    data-assistant-id="{{ assistant.id }}"
                                    data-assistant-name="{{ assistant.name|escapejs }}">
                                <i class="bi bi-star me-1"></i> Rate
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    {% endwith %}

    {% if not assistants %}
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            No community assistants found. Be the first to create one!
        </div>
        <div class="text-center mt-4">
            {% if request.user.is_authenticated %}
                {% if active_company and active_company.entity_type != 'community' %}
                <a href="{% url 'assistants:create' company_id=active_company.id %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i> Create Your First Assistant
                </a>
                {% else %}
                <a href="{% url 'accounts:company_create' %}?type=community" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i> Create Your First Community
                </a>
                {% endif %}
            {% else %}
                <a href="{% url 'accounts:login' %}" class="btn btn-primary">
                    <i class="bi bi-box-arrow-in-right me-2"></i> Log In to Create
                </a>
            {% endif %}
        </div>
    {% endif %}
    </div> {# Close the mt-5 div #}

    {# Pagination with items per page #}
    {% if page_obj.paginator.count > 0 %}
        <div class="mt-4">
            {% include "pagination_with_items_per_page.html" with page_obj=page_obj %}
        </div>
    {% endif %}
    {% csrf_token %} {# Keep CSRF token if needed for other actions on the page #}
</div>

{# Rating Modal Structure #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="ratingForm">
          {% csrf_token %}
          <p>Select your rating for <strong id="modalAssistantName">this assistant</strong>:</p>
          <div class="modal-stars text-center mb-3">
              {% for i_int in "12345" %}
              <button type="button" class="modal-star-btn btn btn-link text-secondary" data-rating-value="{{ i_int }}" title="Rate {{ i_int }} star{{ i_int|pluralize }}">
                  <i class="bi bi-star"></i>
              </button>
              {% endfor %}
          </div>
          <div id="modalErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled onclick="submitRating(event)">Submit Rating</button>
      </div>
    </div>
  </div>
</div>
{# End Rating Modal #}

{# Folder Options Modal for Favorites #}
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="folderOptionsModalLabel">Save to Favorites</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Save <strong id="modalItemName">this item</strong> to:</p>

        {# Quick Save Option #}
        <div class="d-grid gap-2 mb-3">
          <button type="button" class="btn btn-primary save-without-folder-btn">
            <i class="bi bi-heart-fill me-2"></i> Save to Favorites
          </button>
        </div>

        {# Folder Options #}
        <div class="folder-options-container">
          <h6 class="mb-2">Or select a folder:</h6>
          <div id="folderOptionsList" class="list-group mb-3">
            {# Folders will be populated by JavaScript #}
          </div>
        </div>

        {# Create New Folder Option #}
        <div class="create-folder-container">
          <div class="d-grid gap-2">
            <button type="button" class="btn btn-outline-secondary create-folder-toggle-btn">
              <i class="bi bi-folder-plus me-2"></i> Create New Folder
            </button>
          </div>

          <div id="createFolderForm" class="mt-3" style="display: none;">
            <div class="mb-3">
              <label for="newFolderName" class="form-label">Folder Name</label>
              <input type="text" class="form-control" id="newFolderName" placeholder="Enter folder name">
            </div>
            <div class="d-grid gap-2">
              <button type="button" class="btn btn-success create-and-save-btn">
                <i class="bi bi-check-circle me-2"></i> Create Folder & Save
              </button>
            </div>
          </div>
        </div>

        <div id="folderModalErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
      </div>
    </div>
  </div>
</div>
{# End Folder Options Modal #}
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/rating-modal.js' %}"></script>
<script src="{% static 'js/featured-carousel.js' %}"></script>
<script>
// Home page style carousel functionality
document.addEventListener('DOMContentLoaded', function() {
    // Company logo carousel hover effect (like home page)
    const carousel = document.querySelector('.company-logo-carousel');
    if (carousel) {
        const carouselContainer = document.querySelector('.company-logo-carousel-container');

        // Pause animation on hover
        carouselContainer.addEventListener('mouseenter', () => {
            carousel.style.animationPlayState = 'paused';
        });

        // Resume animation when mouse leaves
        carouselContainer.addEventListener('mouseleave', () => {
            carousel.style.animationPlayState = 'running';
        });
    }
});
</script>
<script>
// Fix for filter form in dark mode
document.addEventListener('DOMContentLoaded', function() {
    // Check if dark mode is enabled
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

    if (isDarkMode) {
        // Target the form with class form-filter-form
        const filterForm = document.querySelector('form.form-filter-form, form.filter-form');
        if (filterForm) {
            filterForm.style.backgroundColor = '#121212';
            filterForm.style.border = '1px solid #333333';
            filterForm.style.borderRadius = '8px';
            filterForm.style.padding = '20px';
            filterForm.style.marginBottom = '20px';
            filterForm.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
        }
    }

    // Listen for dark mode toggle
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            setTimeout(function() {
                const isDarkModeNow = document.documentElement.getAttribute('data-theme') === 'dark';
                const filterForm = document.querySelector('form.form-filter-form, form.filter-form');
                if (filterForm) {
                    if (isDarkModeNow) {
                        filterForm.style.backgroundColor = '#121212';
                        filterForm.style.border = '1px solid #333333';
                    } else {
                        filterForm.style.backgroundColor = '';
                        filterForm.style.border = '';
                    }
                }
            }, 100);
        });
    }
});
</script>
<script>
// Direct hover pause fix for community assistants carousel
document.addEventListener('DOMContentLoaded', function() {
    // Direct approach to fix hover pause for community assistants
    const carousel = document.querySelector('.featured-carousel-items');
    const carouselContainer = document.querySelector('.featured-carousel-container');

    if (carousel && carouselContainer) {
        console.log('[DEBUG] Community Assistants: Adding direct hover handlers');

        // Remove any inline animation-play-state
        carousel.style.removeProperty('animation-play-state');

        // Add hover handlers to container
        carouselContainer.addEventListener('mouseenter', function() {
            carousel.style.animationPlayState = 'paused';
            console.log('[DEBUG] Community Assistants: Direct pause on container hover');
        });

        carouselContainer.addEventListener('mouseleave', function() {
            carousel.style.animationPlayState = 'running';
            console.log('[DEBUG] Community Assistants: Direct resume on container leave');
        });

        // Add hover handlers to all items
        const items = carousel.querySelectorAll('.featured-carousel-item');
        items.forEach(function(item) {
            item.addEventListener('mouseenter', function() {
                carousel.style.animationPlayState = 'paused';
                console.log('[DEBUG] Community Assistants: Direct pause on item hover');
            });

            item.addEventListener('mouseleave', function() {
                if (!carouselContainer.matches(':hover')) {
                    carousel.style.animationPlayState = 'running';
                    console.log('[DEBUG] Community Assistants: Direct resume on item leave');
                }
            });
        });
    }
});

// Initialize featured carousel
// The featured carousel is now initialized by featured-carousel.js
// No need to initialize it again here

// Define the base URL for the rating endpoint
const rateAssistantBaseUrl = "/directory/rate-assistant/";

// Rating functionality
let selectedRating = 0;

function selectRating(rating, event) {
    if (event) {
        event.preventDefault();
    }

    selectedRating = rating;

    // Update star appearance
    const stars = document.querySelectorAll('.modal-star-btn i');
    stars.forEach((star, index) => {
        if (index < rating) {
            star.classList.remove('text-secondary');
            star.classList.add('text-warning');
            star.classList.remove('bi-star');
            star.classList.add('bi-star-fill');
        } else {
            star.classList.add('text-secondary');
            star.classList.remove('text-warning');
            star.classList.add('bi-star');
            star.classList.remove('bi-star-fill');
        }
    });

    // Enable submit button
    const submitBtn = document.getElementById('submitRatingBtn');
    if (submitBtn) {
        submitBtn.disabled = false;
    }
}

function submitRating(event) {
    if (event) {
        event.preventDefault();
    }

    if (!selectedRating || selectedRating < 1 || selectedRating > 5) {
        const errorMsg = document.getElementById('modalErrorMsg');
        if (errorMsg) {
            errorMsg.textContent = 'Please select a rating first.';
            errorMsg.style.display = 'block';
        }
        return;
    }

    // Get assistant ID from modal
    const assistantId = document.getElementById('ratingModal').getAttribute('data-assistant-id');
    if (!assistantId) {
        console.error('No assistant ID found in modal');
        return;
    }

    // Submit rating via AJAX
    const csrfToken = getCsrfToken();
    if (!csrfToken) {
        console.error('CSRF token not found');
        return;
    }

    const url = rateAssistantBaseUrl + assistantId + '/';
    const formData = new URLSearchParams({
        'rating': selectedRating
    });

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': csrfToken,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Clear any error messages before hiding the modal
            const errorMsg = document.getElementById('modalErrorMsg');
            if (errorMsg) {
                errorMsg.style.display = 'none';
                errorMsg.textContent = '';
            }

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('ratingModal'));
            if (modal) {
                modal.hide();
            }

            // Update UI if needed
            const ratingDisplay = document.querySelector(`.rating-display[data-assistant-id="${assistantId}"]`);
            if (ratingDisplay) {
                ratingDisplay.textContent = data.new_avg_rating.toFixed(1);
            }

            // Reset selected rating
            selectedRating = 0;
        } else {
            const errorMsg = document.getElementById('modalErrorMsg');
            if (errorMsg) {
                errorMsg.textContent = data.message || 'An error occurred while submitting your rating.';
                errorMsg.style.display = 'block';
            }
        }
    })
    .catch(error => {
        console.error('Error submitting rating:', error);
        const errorMsg = document.getElementById('modalErrorMsg');
        if (errorMsg) {
            errorMsg.textContent = 'An error occurred while submitting your rating.';
            errorMsg.style.display = 'block';
        }
    });
}
</script>
<script>
// Helper function to get CSRF token (for other functionality)
function getCsrfToken() {
    try {
        // First try to get the token from the form
        const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
        if (csrfInput && csrfInput.value) {
            console.log('Found CSRF token in form input');
            return csrfInput.value;
        }

        // Next try to get it from meta tag
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta && csrfMeta.getAttribute('content')) {
            console.log('Found CSRF token in meta tag');
            return csrfMeta.getAttribute('content');
        }

        // Try to get from cookie as fallback
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('csrftoken=')) {
                console.log('Found CSRF token in cookie');
                return cookie.substring('csrftoken='.length, cookie.length);
            }
        }

        console.warn('CSRF token not found in form, meta tag, or cookies');
        return null;
    } catch (error) {
        console.error('Error getting CSRF token:', error);
        return null;
    }
}

// Check CSRF token on page load
document.addEventListener('DOMContentLoaded', function() {
    const csrfToken = getCsrfToken();
    if (!csrfToken) {
        console.error('No CSRF token found on page load');
        // List all forms and meta tags to help debug
        console.log('Forms on page:', document.querySelectorAll('form'));
        console.log('Meta tags on page:', document.querySelectorAll('meta'));
        console.log('CSRF token in cookie:', document.cookie.includes('csrftoken'));
    } else {
        console.log('CSRF token found on page load:', csrfToken.substring(0, 5) + '...');
    }
});

// Initialize favorites functionality when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Community Assistants: Initializing favorites functionality');

    // Make sure the favorites-functionality.js script is loaded
    if (typeof initFavoriteButtons === 'function') {
        // Initialize favorite buttons
        initFavoriteButtons();

        // Initialize folder modal
        initFolderModal();

        // Ensure the folder modal is properly set up with Bootstrap
        const folderModalElement = document.getElementById('folderOptionsModal');
        if (folderModalElement) {
            // Create a new Bootstrap modal instance if it doesn't exist
            if (!bootstrap.Modal.getInstance(folderModalElement)) {
                new bootstrap.Modal(folderModalElement);
                console.log('Community Assistants: Created new Bootstrap modal instance');
            }
        }

        console.log('Community Assistants: Favorites functionality initialized');
    } else {
        console.error('Community Assistants: favorites-functionality.js not loaded properly');
    }
});
</script>
{# Rating Modal #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center">
        <p>Select your rating for <span id="modalAssistantName">this assistant</span>:</p>
        <form id="ratingForm">
          {% csrf_token %}
          <div class="modal-stars d-flex justify-content-center my-4">
            <button type="button" class="modal-star-btn btn" data-rating-value="1">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="modal-star-btn btn" data-rating-value="2">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="modal-star-btn btn" data-rating-value="3">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="modal-star-btn btn" data-rating-value="4">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="modal-star-btn btn" data-rating-value="5">
              <i class="bi bi-star text-secondary"></i>
            </button>
          </div>
          <div id="modalErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
      </div>
    </div>
  </div>
</div>
{# End Rating Modal #}

{# Folder Options Modal #}
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="folderOptionsModalLabel">Save to Favorites</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Save <strong id="modalItemName">this item</strong> to:</p>

        <div class="list-group mb-3" id="folderOptionsList">
          <!-- Folders will be populated here -->
        </div>

        <div class="d-grid gap-2 mb-3">
          <button type="button" class="btn btn-primary save-without-folder-btn" id="saveWithoutFolderBtn">
            <i class="bi bi-heart-fill me-2"></i> Save to Favorites
          </button>
        </div>

        <div class="d-grid gap-2">
          <button type="button" class="btn btn-outline-primary create-folder-toggle-btn" id="createFolderToggleBtn">
            <i class="bi bi-folder-plus me-2"></i> Create New Folder
          </button>
        </div>

        <div id="createFolderForm" style="display: none; margin-top: 15px;">
          <div class="input-group mb-3">
            <span class="input-group-text"><i class="bi bi-folder-plus"></i></span>
            <input type="text" class="form-control" id="newFolderName" placeholder="Enter folder name">
            <button class="btn btn-primary create-and-save-btn" type="button" id="createAndSaveBtn">
              <i class="bi bi-check-lg"></i> Create & Save
            </button>
          </div>
        </div>

        <div id="folderModalErrorMsg" class="alert alert-danger small mt-2" style="display: none;"></div>
      </div>
    </div>
  </div>
</div>
{# End Folder Options Modal #}
<script src="{% static 'js/featured-carousel.js' %}"></script>
<script src="{% static 'js/rating-modal.js' %}"></script>
<script src="{% static 'js/favorites-functionality.js' %}"></script>
<script>
// Helper function to get CSRF token
function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    if (!csrfInput) {
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) return csrfMeta.getAttribute('content');
    }
    return csrfInput ? csrfInput.value : null;
}

// Initialize rating modal functionality
document.addEventListener('DOMContentLoaded', function() {
    // Rating modal functionality
    const ratingModal = document.getElementById('ratingModal');
    if (ratingModal) {
        const modalStars = ratingModal.querySelectorAll('.modal-star-btn');
        const submitBtn = document.getElementById('submitRatingBtn');
        let selectedRating = 0;
        let currentAssistantId = null;

        // Handle star clicks
        modalStars.forEach(star => {
            star.addEventListener('click', function() {
                const value = parseInt(this.getAttribute('data-rating-value'));
                selectedRating = value;

                // Update star appearance
                modalStars.forEach(s => {
                    const starValue = parseInt(s.getAttribute('data-rating-value'));
                    const icon = s.querySelector('i');
                    if (starValue <= selectedRating) {
                        icon.classList.remove('text-secondary');
                        icon.classList.add('text-warning');
                        s.classList.add('active');
                    } else {
                        icon.classList.add('text-secondary');
                        icon.classList.remove('text-warning');
                        s.classList.remove('active');
                    }
                });

                // Enable submit button
                submitBtn.disabled = false;
            });
        });

        // Reset modal when shown
        ratingModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            currentAssistantId = button.getAttribute('data-assistant-id');
            const assistantName = button.getAttribute('data-assistant-name');

            // Update modal title with assistant name
            document.getElementById('modalAssistantName').textContent = assistantName;

            // Reset stars
            selectedRating = 0;
            modalStars.forEach(s => {
                const icon = s.querySelector('i');
                icon.classList.add('text-secondary');
                icon.classList.remove('text-warning');
                s.classList.remove('active');
            });

            // Disable submit button
            submitBtn.disabled = true;

            // Hide error message
            document.getElementById('modalErrorMsg').style.display = 'none';
        });

        // Handle submit button click
        submitBtn.addEventListener('click', async function() {
            if (!selectedRating || !currentAssistantId) {
                document.getElementById('modalErrorMsg').textContent = 'Please select a rating before submitting.';
                document.getElementById('modalErrorMsg').style.display = 'block';
                return;
            }

            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                document.getElementById('modalErrorMsg').textContent = 'CSRF token not found. Please refresh the page.';
                document.getElementById('modalErrorMsg').style.display = 'block';
                return;
            }

            // Show loading state
            const originalButtonText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';
            document.getElementById('modalErrorMsg').style.display = 'none';

            try {
                const response = await fetch(`/directory/rate-assistant/${currentAssistantId}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({
                        'rating': selectedRating
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    // Close modal
                    bootstrap.Modal.getInstance(ratingModal).hide();

                    // Find all possible containers to update (main list and carousel)
                    const containersToUpdate = [
                        document.getElementById(`rating-display-${currentAssistantId}`),
                        document.getElementById(`rating-display-carousel-${currentAssistantId}`),
                        document.getElementById(`rating-display-carousel-dup-${currentAssistantId}`)
                    ].filter(Boolean); // Filter out null/undefined

                    const noRatingPlaceholders = [
                        document.getElementById(`no-rating-placeholder-${currentAssistantId}`),
                        document.getElementById(`no-rating-placeholder-carousel-${currentAssistantId}`),
                        document.getElementById(`no-rating-placeholder-carousel-dup-${currentAssistantId}`)
                    ].filter(Boolean);

                    // If no containers found but placeholders exist, use those
                    if (containersToUpdate.length === 0 && noRatingPlaceholders.length > 0) {
                        noRatingPlaceholders.forEach(placeholder => {
                            placeholder.outerHTML = data.rendered_stars_html;
                        });
                        console.log(`Updated ${noRatingPlaceholders.length} placeholder containers for assistant ${currentAssistantId}`);
                    }

                    // Update all found containers
                    if (containersToUpdate.length > 0 && data.rendered_stars_html) {
                        containersToUpdate.forEach(container => {
                            container.outerHTML = data.rendered_stars_html;
                        });

                        // Show success message
                        const ratingMsg = document.getElementById(`rating-msg-${currentAssistantId}`);
                        if (ratingMsg) {
                            ratingMsg.textContent = 'Rating submitted!';
                            ratingMsg.style.display = 'inline';
                            setTimeout(() => {
                                ratingMsg.style.display = 'none';
                            }, 3000);
                        }

                        console.log(`Successfully updated ${containersToUpdate.length} rating containers for assistant ${currentAssistantId} to ${selectedRating}`);
                    } else if (containersToUpdate.length === 0 && noRatingPlaceholders.length === 0) {
                        console.warn(`Could not find any rating containers for assistant ${currentAssistantId}`);
                    }
                } else {
                    document.getElementById('modalErrorMsg').textContent = data.message || 'An error occurred while submitting your rating.';
                    document.getElementById('modalErrorMsg').style.display = 'block';
                }
            } catch (error) {
                console.error('Error submitting rating:', error);

                // Show detailed error information in console for debugging
                console.error('Rating submission details:', {
                    assistantId: currentAssistantId,
                    rating: selectedRating,
                    endpoint: `/directory/rate-assistant/${currentAssistantId}/`
                });

                // Show user-friendly error message
                document.getElementById('modalErrorMsg').textContent = 'An error occurred while submitting your rating. Please try again later.';
                document.getElementById('modalErrorMsg').style.display = 'block';
            } finally {
                // Restore button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalButtonText;
            }
        });
    }
});
</script>

<!-- Dark mode script for assistant list -->
<script src="{% static 'js/assistant-list-dark-mode.js' %}"></script>
{% endblock %}