{% extends "base/layout.html" %}
{% load static rating_tags assistant_tags %} {# Load rating_tags AND assistant_tags #}

{% block title %}Chat with {{ assistant.name }}{% endblock %}

{% block body_style %}background-color: #FFFFFF !important; background: #FFFFFF !important; background-image: none !important;{% endblock %}

{# Override the navbar block to create a unified header #}
{% block navbar %}
<!-- Unified Chat Header Component -->
<nav class="navbar navbar-expand-lg navbar-light sticky-top unified-chat-header">
    <div class="container-fluid">
        <!-- Mobile Layout (visible on small screens) -->
        <div class="d-lg-none w-100">
            <div class="d-flex align-items-center justify-content-between">
                <!-- Left side: Home + Menu + Company info -->
                <div class="d-flex align-items-center flex-grow-1 me-2">
                    <!-- Home link -->
                    <a class="navbar-brand me-2" href="{% url 'home' %}" style="font-size: 0.9rem; padding: 0;">
                        <i class="bi bi-house"></i>
                    </a>

                    <!-- Menu button (if sidebar enabled) -->
                    {% if assistant.assistant_type != 'general' and assistant.show_sidebar %}
                    <button type="button" id="test-sidebar-toggle-mobile" class="btn btn-success btn-sm me-2" title="Toggle Sidebar" style="box-shadow: 0 2px 5px rgba(0,0,0,0.3); cursor: pointer; font-weight: bold; padding: 4px 8px; border-radius: 4px; flex-shrink: 0;" onclick="var sidebar = document.getElementById('chat-sidebar'); var overlay = document.getElementById('sidebar-overlay'); if(sidebar){sidebar.style.display='block';sidebar.style.visibility='visible';sidebar.classList.toggle('active');sidebar.style.left=sidebar.classList.contains('active')?'0':'-350px'; if(overlay && sidebar.classList.contains('active')){overlay.style.display='block';overlay.style.pointerEvents='auto';setTimeout(()=>{overlay.style.opacity='1';},10);}}">
                        <i class="bi bi-list"></i>
                    </button>
                    {% endif %}

                    <!-- Company logo and name -->
                    {% with logo_url=assistant.get_logo_url %}
                        {% if logo_url %}
                            <img src="{{ logo_url }}" alt="{{ assistant.name }} Logo" style="height: 24px; width: auto; margin-right: 0.5rem; border-radius: 0.25rem; flex-shrink: 0;">
                        {% else %}
                            <i class="bi bi-robot" style="font-size: 18px; margin-right: 0.5rem; flex-shrink: 0;"></i>
                        {% endif %}
                    {% endwith %}

                    <div class="d-flex flex-column min-width-0 flex-grow-1">
                        <span class="fw-bold text-truncate" style="font-size: 0.9rem; line-height: 1.2; color: #ffffff;">{{ assistant.company.name }}</span>
                        <span class="text-muted text-truncate" style="font-size: 0.75rem; line-height: 1.1; color: #adb5bd;">{{ assistant.name }}</span>
                    </div>
                </div>

                <!-- Right side: Rating + User menu + Actions -->
                <div class="d-flex align-items-center flex-shrink-0">
                    <!-- Compact rating display -->
                    <div class="rating-display-container me-2" id="rating-display-mobile-{{ assistant.id }}" style="font-size: 0.75rem;">
                        {% if assistant.listing %}
                            {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                        {% else %}
                            <div class="star-rating"><span class="rating-count ms-1 small text-muted">(No ratings yet)</span></div>
                        {% endif %}
                    </div>

                    <!-- User menu (if authenticated) -->
                    {% if user.is_authenticated %}
                    <div class="dropdown me-2">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                            <i class="bi bi-person-circle"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{% url 'accounts:dashboard' %}"><i class="bi bi-speedometer2 me-1"></i> Dashboard</a></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:user_settings' %}"><i class="bi bi-person me-1"></i> Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="{% url 'accounts:logout' %}">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right me-1"></i> Log Out
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                    {% endif %}

                    <!-- Action buttons dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;">
                            <i class="bi bi-three-dots"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            {% if user.is_authenticated %}
                            <li>
                                <button type="button" class="dropdown-item rate-assistant-btn" data-bs-toggle="modal" data-bs-target="#ratingModal" data-assistant-id="{{ assistant.id }}" data-assistant-name="{{ assistant.name|escapejs }}">
                                    <i class="bi bi-star me-1"></i> Rate
                                </button>
                            </li>
                            <li>
                                <button type="button" class="dropdown-item like-button" data-item-id="{{ assistant.id }}" data-item-type="assistant" title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}">
                                    <i class="bi {% if is_favorited %}bi-heart-fill text-danger{% else %}bi-heart{% endif %} me-1"></i>
                                    {% if is_favorited %}Favorited{% else %}Favorite{% endif %}
                                </button>
                            </li>
                            {% endif %}
                            {% if assistant.qr_code or assistant.linked_company and assistant.linked_company.qr_code %}
                            <li>
                                <button type="button" class="dropdown-item" id="qr-code-btn-mobile" title="Show QR Code" data-bs-toggle="modal" data-bs-target="#qrCodeModal" data-assistant-id="{{ assistant.id }}" {% if assistant.qr_code %}data-qr-url="{{ assistant.qr_code.url }}"{% elif assistant.linked_company and assistant.linked_company.qr_code %}data-qr-url="{{ assistant.linked_company.qr_code.url }}" data-is-company="true" data-company-name="{{ assistant.linked_company.name }}"{% endif %}>
                                    <i class="bi bi-qr-code me-1"></i> QR Code
                                </button>
                            </li>
                            {% endif %}
                            <li>
                                <button type="button" class="dropdown-item" id="copy-url-btn-mobile" title="Copy Chat URL">
                                    <i class="bi bi-share me-1"></i> Share URL
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Desktop Layout (visible on large screens) -->
        <div class="d-none d-lg-flex w-100 align-items-center">
            <!-- Left side: Home + Company info -->
            <div class="d-flex align-items-center me-auto">
                <!-- Home link -->
                <a class="navbar-brand me-3" href="{% url 'home' %}">
                    <i class="bi bi-house me-2"></i> Home
                </a>

                <!-- Company branding -->
                <div class="company-brand d-flex align-items-center">
                    {% with logo_url=assistant.get_logo_url %}
                        {% if logo_url %}
                            <img src="{{ logo_url }}" alt="{{ assistant.name }} Logo" class="company-header-logo me-2" style="height: 32px; width: auto; border-radius: 0.25rem;">
                        {% else %}
                            <i class="bi bi-robot me-2" style="font-size: 24px;"></i>
                        {% endif %}
                    {% endwith %}
                    <div>
                        <div class="company-name">{{ assistant.company.name }}</div>
                        <div style="font-size: 0.8rem; color: #6c757d;">{{ assistant.name }}</div>
                    </div>
                </div>
            </div>

            <!-- Center: Rating -->
            <div class="d-flex align-items-center mx-3">
                <div class="rating-display-container" id="rating-display-desktop-{{ assistant.id }}">
                    {% if assistant.listing %}
                        {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                    {% else %}
                        <div class="star-rating"><span class="rating-count ms-1 small text-muted">(No ratings yet)</span></div>
                    {% endif %}
                </div>
            </div>

            <!-- Right side: Actions + User menu -->
            <div class="d-flex align-items-center">
                <!-- Action buttons -->
                {% if user.is_authenticated %}
                <button type="button" class="btn btn-outline-light btn-sm me-2 rate-assistant-btn" data-bs-toggle="modal" data-bs-target="#ratingModal" data-assistant-id="{{ assistant.id }}" data-assistant-name="{{ assistant.name|escapejs }}">
                    <i class="bi bi-star me-1"></i> Rate
                </button>
                <button type="button" class="btn btn-outline-light btn-sm me-2 like-button" data-item-id="{{ assistant.id }}" data-item-type="assistant" title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}">
                    <i class="bi {% if is_favorited %}bi-heart-fill text-danger{% else %}bi-heart{% endif %} me-1"></i>
                    {% if is_favorited %}Favorited{% else %}Favorite{% endif %}
                </button>
                {% endif %}

                {% if assistant.qr_code or assistant.linked_company and assistant.linked_company.qr_code %}
                <button type="button" class="btn btn-outline-light btn-sm me-2" id="qr-code-btn-desktop" title="Show QR Code" data-bs-toggle="modal" data-bs-target="#qrCodeModal" data-assistant-id="{{ assistant.id }}" {% if assistant.qr_code %}data-qr-url="{{ assistant.qr_code.url }}"{% elif assistant.linked_company and assistant.linked_company.qr_code %}data-qr-url="{{ assistant.linked_company.qr_code.url }}" data-is-company="true" data-company-name="{{ assistant.linked_company.name }}"{% endif %}>
                    <i class="bi bi-qr-code"></i>
                </button>
                {% endif %}

                <button type="button" class="btn btn-outline-light btn-sm me-3" id="copy-url-btn-desktop" title="Copy Chat URL">
                    <i class="bi bi-share me-1"></i> Share
                </button>

                <!-- Menu button (if sidebar enabled) -->
                {% if assistant.assistant_type != 'general' and assistant.show_sidebar %}
                <button type="button" id="test-sidebar-toggle-desktop" class="btn btn-success btn-sm me-3" title="Toggle Sidebar" style="box-shadow: 0 2px 5px rgba(0,0,0,0.3); cursor: pointer; font-weight: bold; padding: 6px 12px; border-radius: 5px;" onclick="var sidebar = document.getElementById('chat-sidebar'); if(sidebar){sidebar.style.display='block';sidebar.style.visibility='visible';sidebar.classList.toggle('active');sidebar.style.left=sidebar.classList.contains('active')?'0':'-350px';}">
                    <i class="bi bi-list me-1"></i> Menu
                </button>
                {% endif %}

                <!-- User menu (if authenticated) -->
                {% if user.is_authenticated %}
                <div class="dropdown">
                    <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-circle me-1"></i> {{ user.username|truncatechars:15 }}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{% url 'accounts:dashboard' %}"><i class="bi bi-speedometer2 me-1"></i> Dashboard</a></li>
                        <li><a class="dropdown-item" href="{% url 'accounts:user_settings' %}"><i class="bi bi-person me-1"></i> Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="post" action="{% url 'accounts:logout' %}">
                                {% csrf_token %}
                                <button type="submit" class="dropdown-item text-danger">
                                    <i class="bi bi-box-arrow-right me-1"></i> Log Out
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
                {% else %}
                <!-- Login/Register for non-authenticated users -->
                <a href="{% url 'accounts:login' %}" class="btn btn-outline-light me-2">Login</a>
                <a href="{% url 'accounts:register' %}" class="btn btn-primary">Register</a>
                {% endif %}
            </div>
        </div>
    </div>
</nav>

<style>
/* Unified Chat Header Styles */
.unified-chat-header {
    background: rgba(248, 249, 250, 0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #dee2e6;
    z-index: 1000;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
}

.unified-chat-header .navbar-brand {
    color: #333333 !important;
    font-weight: 600;
    text-decoration: none;
    transition: color 0.2s ease;
}

.unified-chat-header .navbar-brand:hover {
    color: #E31B23 !important;
}

.unified-chat-header .company-brand {
    border-left: 1px solid #dee2e6;
    padding-left: 1rem;
    margin-left: 0.5rem;
}

.unified-chat-header .company-name {
    font-size: 0.875rem;
    color: #6c757d !important;
    font-weight: 600;
}

.unified-chat-header .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

.unified-chat-header .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: #ffffff;
}

.unified-chat-header .dropdown-menu {
    background-color: rgba(15, 15, 15, 0.95);
    border: 1px solid #444;
    backdrop-filter: blur(10px);
}

.unified-chat-header .dropdown-item {
    color: #ffffff;
}

.unified-chat-header .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

/* Mobile specific adjustments */
@media (max-width: 991.98px) {
    .unified-chat-header {
        padding: 0.5rem 0.75rem;
    }

    .unified-chat-header .star-rating {
        font-size: 0.75rem;
    }

    .unified-chat-header .star-rating .bi-star-fill,
    .unified-chat-header .star-rating .bi-star {
        font-size: 0.75rem;
    }

    /* Hide rating count text on mobile to save space - show only stars */
    .unified-chat-header .rating-count {
        display: none !important;
    }

    /* Hide desktop menu toggle on mobile devices */
    #test-sidebar-toggle-desktop {
        display: none !important;
        visibility: hidden !important;
    }

    /* Completely hide footer on mobile for maximum chat space */
    footer.footer,
    footer,
    .footer {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        position: absolute !important;
        left: -9999px !important;
    }
}

/* Impersonation bar adjustments for assistant chat */
body.is-impersonating .unified-chat-header {
    top: 40px !important; /* Position below impersonation bar */
}

body.is-impersonating .unified-chat-header.sticky-top {
    top: 40px !important; /* Position below impersonation bar */
}

/* Ensure impersonation bar appears above custom navbar */
#impersonation-alert {
    z-index: 1060 !important; /* Higher than unified-chat-header */
    height: 40px !important; /* Ensure consistent height */
}

/* Adjust main content when impersonating in assistant chat */
body.is-impersonating main {
    padding-top: 80px !important; /* Account for both impersonation bar and navbar */
}

/* Adjust chat container positioning when impersonating */
body.is-impersonating .chat-container,
body.is-impersonating .general-chat-container {
    margin-top: 0 !important; /* Remove extra margin since we have padding on main */
}

/* Mobile header fixed positioning */
@media (max-width: 768px) {
    .unified-chat-header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1050 !important;
        width: 100% !important;
        padding: 0.5rem 0.75rem !important;
    }

    /* Adjust for impersonation bar on mobile */
    body.is-impersonating .unified-chat-header {
        top: 40px !important; /* Position below impersonation bar */
    }

    /* Adjust main content on mobile when impersonating */
    body.is-impersonating main {
        padding-top: 90px !important; /* Account for both bars on mobile */
    }

    /* Ensure impersonation bar is properly positioned on mobile */
    #impersonation-alert {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        z-index: 1060 !important;
    }

    /* Hide desktop menu toggle on mobile */
    #test-sidebar-toggle-desktop {
        display: none !important;
        visibility: hidden !important;
    }
}
</style>
{% endblock %}



{% block extra_head %}
<!-- Meta tags for assistant identification -->
<meta name="company-id" content="{{ assistant.company.id }}">
<meta name="assistant-id" content="{{ assistant.id }}">
<meta name="user-id" content="{% if user.is_authenticated %}{{ user.id }}{% else %}anonymous{% endif %}">

<!-- Preload assistant avatar for faster loading -->
{% if assistant.avatar and assistant.avatar.url %}
<link rel="preload" href="{{ assistant.avatar.url }}" as="image" fetchpriority="high">
{% elif default_logo_url %}
<link rel="preload" href="{{ default_logo_url }}" as="image" fetchpriority="high">
{% else %}
    {% with logo_url=assistant.get_logo_url %}
        {% if logo_url %}
<link rel="preload" href="{{ logo_url }}" as="image" fetchpriority="high">
        {% endif %}
    {% endwith %}
{% endif %}

<!-- Direct style overrides -->
<link rel="stylesheet" href="{% static 'css/solid-colors.css' %}">
<link rel="stylesheet" href="{% static 'css/chat-solid-colors.css' %}">
<link rel="stylesheet" href="{% static 'css/override-gradients.css' %}">
<link rel="stylesheet" href="{% static 'css/no-gradients.css' %}">
<link rel="stylesheet" href="{% static 'css/header-text-enhancement.css' %}">
<link rel="stylesheet" href="{% static 'css/header-glass-buttons.css' %}">
<!-- <link rel="stylesheet" href="{% static 'css/assistant-dark-mode.css' %}"> -->
<link rel="stylesheet" href="{% static 'css/sidebar-button-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/final-overrides.css' %}">
<link rel="stylesheet" href="{% static 'css/mobile-chat.css' %}">
<link rel="stylesheet" href="{% static 'css/responsive-chat-bubbles.css' %}">
<link rel="stylesheet" href="{% static 'css/responsive-chat-tables.css' %}">
<link rel="stylesheet" href="{% static 'css/contact-info-tables.css' %}">
<link rel="stylesheet" href="{% static 'css/responsive-message-width.css' %}">
<link rel="stylesheet" href="{% static 'css/thinking-indicator.css' %}">
<link rel="stylesheet" href="{% static 'css/navigation-message-scroll.css' %}">
<style type="text/css">
/* Direct override for the specific HTML structure */
div.message.assistant-message.mb-3 span.message-content {
    background: #F8F9FA !important;
    background-color: #F8F9FA !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #333333 !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

/* Enhanced Like Button Styling - No circles, just hearts */
.like-button {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    width: auto !important;
    height: auto !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    padding: 4px !important;
}

/* Unselected heart - clear/transparent with visible outline */
.like-button .bi-heart {
    color: transparent !important;
    font-size: 1.4rem !important;
    font-weight: 600 !important;
    -webkit-text-stroke: 2px #252638 !important;
    text-stroke: 2px #252638 !important;
    transition: all 0.3s ease !important;
}

/* Selected heart - red/pink and filled */
.like-button .bi-heart-fill,
.like-button.text-danger .bi-heart-fill {
    color: #cf2e2e !important;
    font-size: 1.4rem !important;
    font-weight: 600 !important;
    -webkit-text-stroke: none !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    filter: drop-shadow(0 0 4px rgba(207, 46, 46, 0.4)) !important;
    transition: all 0.3s ease !important;
}

/* Hover effects - no background changes */
.like-button:hover {
    background: transparent !important;
    border: none !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Hover effect for unselected hearts - fill with red */
.like-button:hover .bi-heart {
    color: #cf2e2e !important;
    -webkit-text-stroke: none !important;
    transform: scale(1.1) !important;
}

/* Hover effect for selected hearts */
.like-button:hover .bi-heart-fill {
    transform: scale(1.1) !important;
    filter: drop-shadow(0 0 6px rgba(207, 46, 46, 0.6)) !important;
}

/* Active/clicked animation */
.like-button:active {
    transform: scale(0.95) !important;
}

/* Remove text margins */
.like-button .bi-heart.me-1,
.like-button .bi-heart-fill.me-1 {
    margin-right: 0 !important;
}

/* Pulse animation for when liked */
@keyframes heartPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.like-button .bi-heart-fill.pulse {
    animation: heartPulse 0.6s ease-in-out !important;
}

/* Force background color */
html, body {
    background-color: #e8f4ff !important;
    background: #e8f4ff !important;
    background-image: none !important;
}

/* Force chat container styles */
.chat-container, .general-chat-container {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    background-image: none !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 1.25rem !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Force chat box styles */
.chat-box, .general-chat-box, #chat-box {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    background-image: none !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 1.25rem !important;
}

/* Force user message styles - solid NUP red */
.user-message .message-content,
div.message.user-message.mb-3 span.message-content,
div.message.user-message span.message-content,
.message.user-message .message-content,
.user-message .message-content,
span.message-content.user-message {
    background-color: #cf2e2e !important;
    background: #cf2e2e !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Force assistant message styles - solid dark */
.assistant-message .message-content,
div.message.assistant-message.mb-3 span.message-content,
div.message.assistant-message span.message-content,
.message.assistant-message .message-content,
.assistant-message .message-content,
span.message-content.assistant-message {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #ffffff !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

/* Ensure no gradients are applied */
.message-content::before,
.message-content::after,
span.message-content::before,
span.message-content::after {
    display: none !important;
    content: none !important;
    background: none !important;
    background-image: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* Target the message containers */
div.message.assistant-message.mb-3,
div.message.assistant-message,
.message.assistant-message,
.assistant-message,
div.message.user-message.mb-3,
div.message.user-message,
.message.user-message,
.user-message {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}

/* Force chat form styles */
#chat-form {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    background-image: none !important;
    border-top: 1px solid #dee2e6 !important;
}

/* Force send button styles */
#send-button {
    background-color: #cf2e2e !important;
    background: #cf2e2e !important;
    background-image: none !important;
    color: #ffffff !important;
    border: none !important;
}

/* Force input field styles */
#message-input {
    background-color: #ffffff !important;
    background: #ffffff !important;
    background-image: none !important;
    color: #333333 !important;
    border: 1px solid #d0d0d0 !important;
}
</style>

<!-- Load styles -->
{% if assistant.assistant_type == 'community' %}
<link rel="stylesheet" href="{% static 'css/community-contexts.css' %}">
{% endif %}

<!-- Load console utilities first for browser compatibility -->
<script src="{% static 'js/console-utils.js' %}"></script>

<!-- Immediate script to remove gradients -->
<script>
    // This script runs immediately to remove gradients
    document.addEventListener('DOMContentLoaded', function() {
        // Apply solid colors to all messages
        const assistantMessages = document.querySelectorAll('div.message.assistant-message.mb-3 span.message-content, div.message.assistant-message span.message-content, .message.assistant-message .message-content, .assistant-message .message-content');

        assistantMessages.forEach(function(el) {
            el.style.background = '#F8F9FA';
            el.style.backgroundColor = '#F8F9FA';
            el.style.backgroundImage = 'none';
            el.style.color = '#333333';
            el.style.border = '1px solid #333333';
            el.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.3)';
        });

        // Apply styles once more after a short delay instead of using an interval
        setTimeout(function() {
            assistantMessages.forEach(function(el) {
                el.style.background = '#ffffff';
                el.style.backgroundColor = '#ffffff';
                el.style.backgroundImage = 'none';
            });
        }, 1000);
    });
</script>

<!-- Load chat.js -->
<script src="{% static 'js/rating-variables.js' %}"></script>
<script src="{% static 'js/chat.js' %}" defer></script>
<script src="{% static 'js/chat-interactions.js' %}" defer></script>
<script src="{% static 'js/sidebar-toggle.js' %}" defer></script>
<script src="{% static 'js/navigation-content.js' %}" defer></script>
<script src="{% static 'js/navigation-handler.js' %}" defer></script>
<script src="{% static 'js/enforce-solid-colors.js' %}" defer></script>
<script src="{% static 'js/force-solid-colors.js' %}" defer></script>
<script src="{% static 'js/remove-gradients.js' %}" defer></script>
<!-- <script src="{% static 'js/chat-dark-mode.js' %}" defer></script> -->
<script src="{% static 'js/button-fix.js' %}" defer></script>
<script src="{% static 'js/button-functionality.js' %}" defer></script>
<script src="{% static 'js/chat-table-handler.js' %}" defer></script>
<script src="{% static 'js/responsive-message-bubbles.js' %}" defer></script>
<script src="{% static 'js/context-preloader.js' %}" defer></script>
<script src="{% static 'js/navigation-scroll-test.js' %}" defer></script>

<!-- Immediate script to hide welcome message if there are existing messages or chat history -->
<script>
    // Immediately check for existing messages and hide welcome message if found
    (function() {
        try {
            // Check for existing messages in the DOM
            const existingMessages = document.querySelectorAll('.message:not(.initial-greeting)');
            const userMessages = document.querySelectorAll('.user-message');

            // Hide welcome message if there are any messages, especially user messages
            if (existingMessages.length > 0 || userMessages.length > 0) {
                const initialDisplayArea = document.getElementById('initial-display-area');
                if (initialDisplayArea) {
                    initialDisplayArea.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; height: 0 !important; overflow: hidden !important; margin: 0 !important; padding: 0 !important;';
                    console.log('Initial display area immediately hidden by inline script due to existing messages');
                }
            }

            // Also check session storage for chat history
            const assistantId = document.querySelector('meta[name="assistant-id"]')?.content || 'default';
            const sessionStorageKey = `chatHistory_${assistantId}`;
            const storedHistory = sessionStorage.getItem(sessionStorageKey);

            if (storedHistory) {
                try {
                    const history = JSON.parse(storedHistory);
                    if (Array.isArray(history) && history.length > 0) {
                        // Check if there's at least one user message in the history
                        const hasUserMessage = history.some(msg => msg.role === 'user');

                        if (hasUserMessage) {
                            const initialDisplayArea = document.getElementById('initial-display-area');
                            if (initialDisplayArea) {
                                initialDisplayArea.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; height: 0 !important; overflow: hidden !important; margin: 0 !important; padding: 0 !important;';
                                console.log('Initial display area immediately hidden due to user messages in chat history');
                            }
                        }
                    }
                } catch (e) {
                    console.error('Failed to parse chat history:', e);
                }
            }
        } catch (error) {
            console.error('Error in immediate welcome message check:', error);
        }
    })();
</script>

<!-- Load script to hide welcome message -->
<script src="{% static 'js/hide-welcome-message.js' %}"></script>

<!-- Sidebar toggle functionality is handled in sidebar-toggle.js -->

<!-- Assistant custom styles -->
{% if assistant.custom_css %}
<script type="module">
    // Enhanced CSS Validator and Injector
    const CSSManager = {
        css: `{{ assistant.custom_css|escapejs }}`,
        scope: `assistant-${Date.now()}`,

        // Validates and sanitizes CSS rules
        validateCSS(css) {
            try {
                // Remove potentially harmful patterns
                const sanitized = css
                    .replace(/@import\s+/gi, '/* @import not allowed */')
                    .replace(/@charset\s+/gi, '/* @charset not allowed */')
                    .replace(/behaviour\s*:/gi, '/* behavior not allowed */')
                    .replace(/-moz-binding\s*:/gi, '/* binding not allowed */');

                // Basic structural validation
                if (!/^[^<>]*$/.test(sanitized)) {
                    throw new Error('Invalid characters detected');
                }

                // Parse and validate selectors
                const parsed = this.parseRules(sanitized);
                if (!parsed) {
                    throw new Error('Failed to parse CSS');
                }

                return parsed;
            } catch (error) {
                console.error('CSS Validation failed:', error);
                return null;
            }
        },

        // Parses and structures CSS rules
        parseRules(css) {
            try {
                // Create temporary stylesheet for parsing
                const sheet = new CSSStyleSheet();
                return sheet.replace(css)
                    .then(() => {
                        // Add scoping to all rules
                        return css.replace(
                            /([^{}]*)({[^}]*})/g,
                            (match, selector, rules) => {
                                if (selector.trim()) {
                                    // Scope each selector
                                    const scoped = selector.split(',')
                                        .map(s => `.${this.scope} ${s.trim()}`)
                                        .join(',');
                                    return `${scoped}${rules}`;
                                }
                                return match;
                            }
                        );
                    });
            } catch (error) {
                console.error('CSS Parsing failed:', error);
                return null;
            }
        },

        // Injects the processed CSS
        async inject() {
            try {
                // Validate and process CSS
                const validCSS = await this.validateCSS(this.css);
                if (!validCSS) {
                    throw new Error('CSS validation failed');
                }

                // Add base reset for scoped styles
                const finalCSS = `.${this.scope}{all:revert;}\n${validCSS}`;

                // Try constructable stylesheets first
                if ('adoptedStyleSheets' in document) {
                    const sheet = new CSSStyleSheet();
                    await sheet.replace(finalCSS);
                    document.adoptedStyleSheets = [...document.adoptedStyleSheets, sheet];
                } else {
                    // Fallback to traditional style element
                    const style = document.createElement('style');
                    style.textContent = finalCSS;
                    document.head.appendChild(style);
                }

                // Apply scope class
                document.body.classList.add(this.scope);

            } catch (error) {
                console.error('Style injection failed:', error);
                // No fallback - if validation fails, styles are not applied
            }
        }
    };

    // Initialize after DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => CSSManager.inject());
    } else {
        CSSManager.inject();
    }
</script>
{% endif %}

<style type="text/css">
/* Avatar icon styles */
.chat-avatar-icon {
    font-size: 2rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

/* Optimize avatar size for mobile */
@media (max-width: 768px) {
    .chat-avatar-icon {
        font-size: 1.5rem;
        width: 30px;
        height: 30px;
    }

    /* Keep the avatar size in messages smaller for better chat layout */
    .message img.rounded-circle {
        width: 30px !important;
        height: 30px !important;
    }

    /* Adjust general avatar for mobile */
    .general-avatar {
        width: 120px !important;
        height: 120px !important;
    }

    .general-avatar-placeholder {
        font-size: 120px !important;
        width: 120px !important;
        height: 120px !important;
        line-height: 120px !important;
    }
}

/* Enhanced Chat Bubble Styling */
.message {
    padding: 0;
    border-radius: 1rem;
    line-height: 1.5;
    box-shadow: none;
    word-wrap: break-word;
    overflow-wrap: break-word;
    display: flex;
    width: 100%;
    margin: 0.5rem 0;
    font-size: 0.95rem;
    letter-spacing: 0.01em;
    font-weight: 450;
    transition: all 0.2s ease;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    box-sizing: border-box !important;
    flex-shrink: 0 !important;
    align-items: flex-start !important; /* Align items at the top */
}

.user-message {
    justify-content: flex-end !important;
}

.assistant-message {
    justify-content: flex-start !important;
}

.message-content {
    max-width: 100%;
    width: 100%;
    border-radius: 1rem;
    padding: 0.8rem 1rem !important;
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: none !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
    white-space: normal !important;
}

/* Responsive adjustments for mobile devices */
@media (max-width: 768px) {
    .message-content {
        padding: 0.7rem 0.9rem !important;
        font-size: 0.95rem !important;
    }

    /* Mobile message styling is now handled by responsive-message-width.css */

    .message {
        padding: 0.3rem 0.5rem;
        margin: 0.4rem 0;
    }

    .chat-box {
        padding: 5px 5px 70px 5px;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    /* Ensure images are properly sized on mobile */
    .message-content img {
        max-width: 100% !important;
        height: auto !important;
    }

    /* Make tables scrollable on mobile */
    .message-content table {
        display: block !important;
        width: 100% !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }
}

/* Small mobile devices */
@media (max-width: 576px) {
    .message-content {
        padding: 0.6rem 0.8rem !important;
        font-size: 0.9rem !important;
    }

    /* Small mobile message styling is now handled by responsive-message-width.css */
}

.user-message .message-content {
    background: #cf2e2e !important; /* Solid NUP red background - matching send button */
    color: #ffffff !important; /* White text color */
    text-align: left !important;
    border-radius: 1.25rem !important;
    border-bottom-right-radius: 0.3rem !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    font-weight: 450 !important;
    border: none !important;
    transform: translateZ(0) !important;
    transition: all 0.2s ease !important;
    padding: 1rem 1.25rem !important;
    line-height: 1.6 !important;
    font-size: 0.95rem !important;
    /* Width is now handled by responsive-message-width.css */
    box-sizing: border-box !important;
    margin: 0 !important;
}

.assistant-message .message-content {
    background: #ffffff !important; /* Solid white background */
    color: #333333 !important; /* Dark text color */
    text-align: left !important;
    border-radius: 1.25rem !important;
    border-bottom-left-radius: 0.3rem !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    font-weight: 450 !important;
    border: none !important;
    transform: translateZ(0) !important;
    transition: all 0.2s ease !important;
    padding: 1rem 1.25rem !important;
    line-height: 1.6 !important;
    font-size: 0.95rem !important;
    /* Width is now handled by responsive-message-width.css */
    box-sizing: border-box !important;
    margin: 0 !important;
}

/* Override any inline styles */
.message-content {
    display: inline-block !important; /* Changed to inline-block for responsive width */
    /* Width is now handled by responsive-message-width.css */
    line-height: 1.6 !important;
    font-size: 1rem !important;
    letter-spacing: 0.01em;
    font-weight: 450;
    border-left: none !important;
    padding: 0.9rem 1.2rem !important;
    padding-right: 1.2rem !important; /* Ensure right padding is consistent */
    position: relative !important;
    overflow: hidden !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    flex: 0 1 auto !important; /* Changed to allow shrinking */
}

/* Remove glass-like reflections */
.message-content::before {
    display: none !important;
}

/* Remove subtle inner shadow for depth */
.message-content::after {
    display: none !important;
}

/* Ensure text is readable */
.message-content p,
.message-content div,
.message-content li,
.message-content ul,
.message-content ol,
.message-content pre {
    position: relative !important;
    z-index: 3 !important;
    margin-bottom: 0.75rem !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    box-sizing: border-box !important;
    display: block !important;
}

/* Exception for inline elements */
.message-content a,
.message-content strong,
.message-content em,
.message-content code,
.message-content span:not(.message-content):not(.tinymce-content) {
    position: relative !important;
    z-index: 3 !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    width: auto !important;
    max-width: 100% !important;
    display: inline !important;
}

/* Ensure images are responsive */
.message-content img {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 0.5rem !important;
    margin: 0.5rem 0 !important;
}

/* Make tables responsive */
.message-content table {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0.5rem 0 !important;
    border-collapse: collapse !important;
    table-layout: fixed !important;
    box-sizing: border-box !important;
    display: table !important; /* Ensure proper display */
}

@media (max-width: 768px) {
    .message-content table {
        display: block !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    .message-content table td,
    .message-content table th {
        min-width: 100px !important; /* Ensure cells have a minimum width */
        white-space: normal !important; /* Allow text to wrap */
        word-break: break-word !important;
    }
}

.message-content p:last-child,
.message-content ul:last-child,
.message-content ol:last-child {
    margin-bottom: 0 !important;
}

.message-content strong {
    font-weight: 600 !important;
}

.message-content code {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace !important;
    background-color: rgba(0, 0, 0, 0.05) !important;
    padding: 0.1em 0.3em !important;
    border-radius: 3px !important;
    font-size: 0.9em !important;
    color: #333333 !important;
}

.message-content pre {
    background-color: rgba(0, 0, 0, 0.05) !important;
    padding: 0.5rem !important;
    border-radius: 5px !important;
    overflow-x: auto !important;
    margin: 0.75rem 0 !important;
}

.message-content pre code {
    background-color: transparent !important;
    padding: 0 !important;
    font-size: 0.9em !important;
}

.message-content a {
    color: #0066cc !important; /* Darker blue for light theme */
    text-decoration: none !important;
}

/* Suggestions area styling - REMOVED */

.message-content a:hover {
    text-decoration: underline !important;
}

.user-message .message-content a {
    color: #ffffff !important; /* White links in user messages */
    text-decoration: underline !important;
}

.assistant-message .message-content a {
    color: #0066cc !important; /* Blue links in assistant messages */
}

/* Hide all unnecessary elements except headers */
.navbar,
.sidebar,
.footer,
.reset-chat-btn,
.nav-tabs,
.tab-content > .tab-pane:not(.active),
#chat-sidebar,
#sidebar-toggle-btn,
#sidebar-toggle-inside,
#sidebar-overlay,
.list-group,
.nav-section-content-block,
.context-links,
.context-links-header,
.context-links-container {
    display: none !important;
}

/* Ensure headers are visible */
.general-assistant-header,
.assistant-header {
    display: block !important;
}

/* Ensure only the chat bubbles are visible */
body {
    background-color: white !important;
    padding: 0 !important;
    margin: 0 !important;
}

.container-fluid {
    padding: 0 !important;
    margin: 0 !important;
}

/* Make sure avatars are visible */
.chat-avatar-icon,
.assistant-profile-pic,
.message img.rounded-circle {
    display: inline-block !important;
}

.user-message:hover .message-content {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px) !important;
    background: #3069b9 !important; /* Slightly darker blue on hover - matching send button hover */
}

.assistant-message:hover .message-content {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-2px) !important;
    background: #ffffff !important; /* Keep solid white on hover */
}

/* Add perspective for 3D effect */
.chat-box {
    perspective: 1000px !important;
}

/* Ensure the chat interface is properly displayed */
html, body {
    height: 100% !important;
    overflow-x: hidden !important;
    background-color: #e8f4ff !important; /* Solid light blue background */
    background: #e8f4ff !important;
    background-image: none !important;
    color: #333333 !important;
}

main {
    padding: 0 !important;
    margin: 0 !important;
}

/* Ensure the chat messages are properly displayed */
.message {
    margin: 1rem 0 !important;
    max-width: 90% !important;
}

.user-message {
    margin-left: auto !important;
    margin-right: 1rem !important;
}

.assistant-message {
    margin-right: auto !important;
    margin-left: 1rem !important;
}

/* Add spacing between messages from the same sender */
.user-message + .user-message,
.assistant-message + .assistant-message {
    margin-top: 0.3rem !important;
}

/* Ensure the chat form is properly positioned - removed fixed positioning */
#chat-form {
    position: relative !important;
    background-color: #f0f0f0 !important; /* Solid light gray background */
    background: #f0f0f0 !important;
    background-image: none !important;
    padding: 10px !important;
    z-index: 10 !important;
    border: 1px solid #e0e0e0 !important; /* Solid light border */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
    border-radius: 1.5rem !important;
    overflow: hidden !important;
}

/* Style the input field with solid colors */
#message-input {
    background-color: #ffffff !important; /* Solid white background */
    color: #333333 !important;
    border: 1px solid #d0d0d0 !important; /* Solid light gray border */
    border-radius: 1.5rem !important;
    padding: 0.75rem 1.25rem !important;
    font-size: 0.95rem !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.2s ease !important;
}

#message-input:focus {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    border-color: #4a90e2 !important; /* Solid blue border on focus */
    background-color: #ffffff !important; /* Keep solid white when focused */
    outline: none !important;
}

/* Style the send button with solid color */
#send-button {
    background-color: #cf2e2e !important; /* Solid NUP red - matching user message bubble */
    color: #ffffff !important;
    border: none !important;
    border-radius: 1.5rem !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.2s ease !important;
}

#send-button:hover {
    background-color: #252638 !important; /* NUP dark accent on hover */
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

.general-avatar {
    width: 190px !important; /* Doubled size for general assistant */
    height: 190px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: 4px solid rgba(255, 255, 255, 0.2) !important;
    object-fit: cover !important;
}

.general-avatar-placeholder {
    font-size: 190px !important; /* Doubled size for general assistant */
    width: 190px !important;
    height: 190px !important;
    line-height: 190px !important;
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Adjust welcome text size for general assistant */
.general-chat-box #initial-display-area .welcome-text-small,
.general-welcome-text {
    font-size: 1.9rem !important; /* Larger welcome text */
    font-weight: bold !important;
}

/* Positioning for initial display area */
#initial-display-area {
    position: relative;
    margin-top: 20px; /* Reduced space above the welcome message */
    display: block !important;
}

/* Community-specific styles */
.community-initial-display {
    margin-top: 10px !important; /* Less space for community chat */
}

/* Position chat box directly below header for both community and customer care */
.community-chat-box, .chat-container {
    margin-top: 0 !important;
    padding-top: 0 !important;
    position: relative;
    top: 0;
    width: 100% !important;
    max-width: 100% !important;
}

/* Ensure chat container has no extra spacing and aligns with header */
.tab-pane .chat-container {
    margin-top: 0;
}

/* Adjust spacing for community tabs to be closer to header */
#communityTabs {
    margin-top: 0;
    margin-bottom: 0 !important;
    border-bottom: none;
}

/* Fix the message input at the bottom of the chat container */
#chat-form {
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    background-color: transparent;
    padding: 8px 0;
    z-index: 100;
    border-radius: 0;
    box-shadow: none;
    border: none;
    margin: 0.5rem 0;
    transition: none;
    margin-top: auto !important;
}

/* Mobile fixed input form styles */
@media (max-width: 768px) {
    /* Fixed input form at bottom */
    #fixed-chat-input {
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1040 !important;
        background-color: #ffffff !important;
        border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
        padding: 0.75rem !important;
    }

    /* Style the input and buttons in fixed form */
    #fixed-chat-input #message-input {
        background-color: #f8f9fa !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
        border-radius: 1.5rem !important;
        padding: 0.75rem 1rem !important;
        font-size: 0.9rem !important;
    }

    #fixed-chat-input #send-button {
        background-color: #cf2e2e !important;
        border: none !important;
        border-radius: 1.5rem !important;
        font-size: 0.9rem !important;
        color: #ffffff !important;
        min-width: 50px !important; /* Ensure button is wide enough for icon */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 0.75rem 0.9rem !important; /* Compact padding for mobile */
    }

    /* Adjust send button padding for desktop */
    @media (min-width: 768px) {
        #fixed-chat-input #send-button {
            padding: 0.75rem 1.5rem !important; /* More padding for desktop */
        }
    }

    #fixed-chat-input #reset-chat-btn {
        font-size: 0.8rem !important;
        padding: 0.5rem 0.75rem !important;
        border-radius: 1rem !important;
        white-space: nowrap !important; /* Prevent text wrapping */
    }

    /* Ensure reset button text doesn't wrap */
    #fixed-chat-input #reset-chat-btn .d-md-none {
        font-size: 0.8rem !important;
    }

    /* Style the send icon on mobile */
    #fixed-chat-input #send-button .bi-send-fill {
        font-size: 1rem !important;
    }

    /* Optimize input field spacing on mobile */
    #fixed-chat-input .row.g-2 {
        gap: 0.5rem !important;
    }

    /* Ensure proper button sizing */
    #fixed-chat-input .col-auto {
        flex: 0 0 auto !important;
    }

    /* Make chat container take full width on mobile */
    .chat-container, .community-chat-box, .chat-box {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    /* Remove bottom padding from chat box since input is now fixed */
    .chat-box {
        padding-bottom: 0.75rem !important;
    }

    /* Ensure body has space for fixed header and input */
    body {
        padding-top: 70px !important; /* Space for fixed header */
        padding-bottom: 90px !important; /* Space for fixed input */
    }
}

#chat-form:focus-within {
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

#message-input {
    border-radius: 1rem;
    padding: 0.7rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.08);
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    color: #333333;
    font-size: 0.95rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    letter-spacing: 0.01em;
    font-weight: 450;
}

#message-input:focus {
    background-color: #ffffff;
    border-color: rgba(0, 0, 0, 0.15);
    box-shadow: none;
}

#send-button {
    border-radius: 1rem;
    padding: 0.5rem 1rem;
    background: #0d6efd;
    border: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 0.01em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

#send-button:hover {
    background: #0b5ed7;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

/* Ensure the chat box aligns with the assistant header */
.chat-box {
    min-height: 300px;
    max-height: calc(100vh - 250px);
    overflow-y: auto !important;
    overflow-x: hidden !important;
    padding: 15px 20px 70px 20px; /* Space for the input form */
    margin-top: 0 !important;
    background-color: #ffffff;
    border-radius: 0.8rem;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.02);
    scrollbar-width: thin;
    scrollbar-color: #d1d1d1 #f1f1f1;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex !important;
    flex-direction: column !important;
    -webkit-overflow-scrolling: touch !important;
}

.chat-box::-webkit-scrollbar {
    width: 8px;
}

.chat-box::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.chat-box::-webkit-scrollbar-thumb {
    background: #d1d1d1;
    border-radius: 10px;
}

.chat-box::-webkit-scrollbar-thumb:hover {
    background: #b1b1b1;
}

/* Ensure the chat container starts at the same position as the assistant header */
.general-assistant-header + .row .chat-container {
    margin-top: 0 !important;
}

/* Remove any extra spacing between header and chat container */
.general-assistant-header {
    margin-bottom: 0 !important;
}

/* Ensure the chat container is properly positioned */
.row .tab-content {
    margin-top: 0 !important;
}

/* Optimize container and row for mobile */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 5px !important;
        padding-right: 5px !important;
    }

    .row {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    .col-md-9, .col-md-12, .col-12 {
        padding-left: 5px !important;
        padding-right: 5px !important;
    }

    /* Ensure the chat interface takes up the full screen width */
    .chat-container {
        border-radius: 0 !important;
        box-shadow: none !important;
        border-left: none !important;
        border-right: none !important;
    }
}

/* Make sure the welcome message is visible and styled nicely */
.initial-greeting {
    display: block !important;
}

/* Don't hide the initial display area by default */
#initial-display-area {
    display: block !important;
}

.d-flex.justify-content-between {
    display: none !important;
}

/* Suggestions styling - REMOVED */

/* Chat container styling that works with light mode */
.chat-container {
    background: #FFFFFF !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    padding: 1rem !important;
    margin: 0 auto !important;
    border-radius: 12px !important;
}

.chat-box {
    background: #FFFFFF !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    padding: 1.5rem !important;
    margin: 0 !important;
    border-radius: 8px !important;
}

/* Dark mode specific overrides */
[data-theme="dark"] .chat-container {
    background: linear-gradient(145deg, #1a1a1a, #151515) !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

[data-theme="dark"] .chat-box {
    background: #FFFFFF !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

/* Hide all unnecessary containers and divs */
.row, .col-12, .tab-content, .tab-pane {
    padding: 0 !important;
    margin: 0 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

/* Optimize welcome message for mobile */
@media (max-width: 768px) {
    .initial-greeting {
        padding: 1rem !important;
        max-width: 95% !important;
        font-size: 1.4rem !important;
        margin: 0.5rem auto !important;
    }

    /* Reduce initial display area spacing */
    #initial-display-area {
        margin-top: 10px;
    }

    /* Make the assistant profile pic responsive on mobile but still prominent */
    .assistant-profile-pic {
        width: 120px !important;
        height: 120px !important;
        margin-bottom: 1rem !important;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
        border: 3px solid rgba(255, 255, 255, 0.2) !important;
    }

    /* Adjust the placeholder icon size for mobile */
    .assistant-profile-pic-placeholder {
        font-size: 120px !important;
        width: 120px !important;
        height: 120px !important;
        line-height: 120px !important;
    }
}

/* Sidebar styles - for both mobile and desktop */
#chat-sidebar {
    position: fixed;
    top: 56px; /* Start below the header */
    left: -350px;
    width: 80%;
    max-width: 300px;
    height: calc(100vh - 56px); /* Adjust height to account for header */
    z-index: 1080; /* Higher z-index to ensure it's above everything */
    background-color: #fff;
    overflow-y: auto;
    transition: left 0.3s ease;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    transform: translateZ(0); /* Force hardware acceleration for smoother transitions */
    display: block;
    visibility: visible;
    opacity: 1;
}

#chat-sidebar.active {
    left: 0;
}

#sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1070; /* Just below sidebar but above content */
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none; /* Allow clicks to pass through when inactive */
}

#sidebar-overlay.active {
    display: block;
    opacity: 1;
    pointer-events: auto; /* Capture clicks when active */
}

/* Desktop sidebar adjustments */
@media (min-width: 768px) {
    #chat-sidebar {
        /* Keep the fixed positioning for desktop too */
        position: fixed;
        left: -350px; /* Start off-screen with a bit more space */
        width: 350px;
        max-width: 350px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
        z-index: 1080; /* Higher z-index to ensure it's above everything */
        top: 56px; /* Start below the header */
        height: calc(100vh - 56px); /* Adjust height to account for header */
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Ensure the main content takes full width and doesn't shift */
    .col-md-9, .col-md-12, .col-12 {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
        transition: none !important;
        position: relative;
        z-index: 1; /* Lower z-index to ensure sidebar overlays */
        margin-left: 0 !important; /* Ensure no margin shift */
        margin-right: 0 !important;
        transform: none !important; /* Prevent any transforms */
    }

    /* Ensure the chat container doesn't shift when sidebar is toggled */
    .chat-container, #communityTabs, .tab-content {
        transition: none !important;
        transform: none !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }

    /* Style the sidebar toggle buttons */
    #sidebar-toggle-btn-mobile, #sidebar-toggle-btn-desktop {
        position: fixed;
        z-index: 1090;
        background-color: #ffffff;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    #sidebar-toggle-btn-mobile:hover, #sidebar-toggle-btn-desktop:hover {
        background-color: #f8f9fa;
        transform: scale(1.05);
    }

    #sidebar-toggle-btn-mobile {
        top: 70px;
        left: 15px;
    }

    #sidebar-toggle-btn-desktop {
        top: 70px;
        left: 15px;
    }
}

/* TinyMCE editor styles */
.tox-tinymce {
    border-radius: 0.25rem !important;
    border: 1px solid #ced4da !important;
    box-shadow: none !important;
    width: 100% !important;
    max-width: none !important;
}

.tox .tox-statusbar {
    border-top: 1px solid #ced4da !important;
}

.tox .tox-toolbar, .tox .tox-toolbar__overflow, .tox .tox-toolbar__primary {
    background: #f8f9fa !important;
}

/* Fix for TinyMCE visibility issue - more aggressive approach */
.tox-tinymce {
    visibility: visible !important;
    display: block !important;
    opacity: 1 !important;
}

.tox-tinymce[style*="visibility: hidden"] {
    visibility: visible !important;
}

/* Additional TinyMCE styling */
.tox-editor-container {
    background-color: white !important;
    visibility: visible !important;
    width: 100% !important;
}

.tinymce-editor {
    min-height: 300px;
}

/* Make sure the editor is visible in the contributions tab */
#contributions-pane .tox-tinymce {
    display: block !important;
    visibility: visible !important;
}

/* Fix for TinyMCE iframe */
.tox-edit-area__iframe {
    visibility: visible !important;
    display: block !important;
    width: 100% !important;
}

/* Ensure TinyMCE toolbar is visible */
.tox-toolbar__primary {
    visibility: visible !important;
    display: flex !important;
}

/* Fix for TinyMCE menu bar */
.tox-menubar {
    visibility: visible !important;
    display: flex !important;
}

/* Fix for TinyMCE edit area */
.tox-edit-area {
    width: 100% !important;
}

/* Enhanced Context Links Styling */
.context-links {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    background: #f8f9fa;
    border-radius: 0.8rem;
    padding: 0.8rem 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.context-links-header {
    color: #6c757d;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 0.85rem;
    letter-spacing: 0.01em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.context-links-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.6rem;
    margin-top: 0.5rem;
}

.context-link {
    color: #0d6efd;
    text-decoration: none;
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
    background: #f0f7ff;
    border-radius: 0.4rem;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    font-weight: 450;
    letter-spacing: 0.01em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.context-link:hover {
    color: #0a58ca;
    background: #e6f0fd;
    text-decoration: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

/* Context Modal Styling */
.context-content {
    font-size: 0.95rem;
    line-height: 1.65;
    white-space: pre-wrap;
    padding: 1.1rem;
    background: #f0f7ff;
    border-radius: 0.7rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(0, 0, 0, 0.05);
    letter-spacing: 0.01em;
    color: #333333;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-weight: 450;
}

.context-metadata {
    margin-left: 1rem;
    padding: 0.3rem 0.6rem;
    background: #f8f9fa;
    border-radius: 0.4rem;
    font-size: 0.8rem;
    font-weight: 450;
    color: #6c757d;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    letter-spacing: 0.01em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Navigation Content Bubble Styling */
.nav-content-bubble .message-content {
    background: #f0f7ff !important;
    border-left: 3px solid #0d6efd !important; /* Blue left border */
    border-radius: 0.8rem !important;
    padding: 0.8rem 1rem !important;
    color: #333333 !important;
}

.nav-content-bubble {
    margin-bottom: 1.2rem !important; /* Add more space between navigation bubbles */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid #333333 !important;
    background: #F8F9FA !important;
}

.nav-content-bubble:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
}

/* Additional overrides to ensure styles are applied */
.message.assistant-message {
    background-color: transparent !important;
    background: transparent !important;
}

.message.user-message {
    background-color: transparent !important;
    background: transparent !important;
}

/* Force override any inline styles */
[style*="background"] {
    background: inherit !important;
}

/* Ensure text is visible */
.message.assistant-message a {
    color: #0d6efd !important;
    text-decoration: underline !important;
    font-weight: bold !important;
}

.message.user-message a {
    color: #0d6efd !important;
    text-decoration: underline !important;
    font-weight: bold !important;
}
</style>

{% if assistant.assistant_type == 'community' %}
<!-- Load TinyMCE using Django's form media -->
{{ form.media.css }}

<!-- Enhanced tab switching script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if URL has a hash for a specific tab
        const hash = window.location.hash;
        if (hash) {
            const tabId = hash.replace('#', '') + '-tab';
            const tab = document.getElementById(tabId);
            if (tab) {
                setTimeout(function() {
                    tab.click();
                }, 100);
            }
        }

        // Ensure tabs remain visible when clicked
        const communityTabs = document.querySelectorAll('#communityTabs .nav-link');
        communityTabs.forEach(tab => {
            tab.addEventListener('click', function(e) {
                // Update hash without triggering scroll
                const targetId = this.getAttribute('data-bs-target').replace('#', '');
                history.replaceState(null, null, '#' + targetId);

                // Ensure the tabs container remains visible
                document.getElementById('communityTabs').style.display = 'flex';

                // Manually activate the tab and show the content
                // Remove active class from all tabs
                communityTabs.forEach(t => {
                    t.classList.remove('active');
                    t.setAttribute('aria-selected', 'false');
                });

                // Add active class to clicked tab
                this.classList.add('active');
                this.setAttribute('aria-selected', 'true');

                // Hide all tab panes
                const tabPanes = document.querySelectorAll('.tab-pane');
                tabPanes.forEach(pane => {
                    pane.classList.remove('show', 'active');
                });

                // Show the target tab pane
                const targetPane = document.querySelector(this.getAttribute('data-bs-target'));
                if (targetPane) {
                    targetPane.classList.add('show', 'active');
                }
            });
        });
    });
</script>
{% endif %}

<!-- Debug script to log website data -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Log website data to console
        console.log('DEBUG - Website Data Keys:', Object.keys(window.websiteData || {}));

        // Log navigation items
        const navItems = document.querySelectorAll('.nav-section-content-block');
        console.log('DEBUG - Navigation Items Found:', navItems.length);
        navItems.forEach((item, index) => {
            console.log(`DEBUG - Navigation Item ${index}:`, {
                id: item.id,
                label: item.getAttribute('data-nav-label'),
                contentPreview: item.innerHTML.substring(0, 100) + '...',
                isEmpty: item.innerHTML.trim() === '',
                isPlaceholder: item.innerHTML.includes('Please click on this section')
            });
        });
    });
</script>

<!-- Store website data in a JavaScript variable -->
<script>
    window.websiteData = JSON.parse('{{ website_data|escapejs|default:"{}" }}');
</script>

<!-- Script to ensure chat bubble styling is applied -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to apply styles to messages
        function applyMessageStyles() {
            // Apply styles to assistant messages
            document.querySelectorAll('.assistant-message').forEach(function(el) {
                el.style.background = '#F8F9FA';
                el.style.color = '#333333';
                el.style.borderColor = '#dee2e6';
                el.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif';
                el.style.fontSize = '0.95rem';
                el.style.letterSpacing = '0.01em';
                el.style.fontWeight = '450';
                el.style.lineHeight = '1.65';

                // Apply to message content inside
                const content = el.querySelector('.message-content');
                if (content) {
                    content.style.background = '#f0f7ff';
                    content.style.color = '#333333';
                    content.style.borderLeft = 'none';
                    content.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif';
                    content.style.fontSize = '0.95rem';
                    content.style.letterSpacing = '0.01em';
                    content.style.fontWeight = '450';
                    content.style.lineHeight = '1.65';
                }
            });

            // Apply styles to user messages
            document.querySelectorAll('.user-message').forEach(function(el) {
                el.style.background = '#f8f9fa';
                el.style.color = '#333333';
                el.style.borderColor = 'rgba(0, 0, 0, 0.05)';
                el.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif';
                el.style.fontSize = '0.95rem';
                el.style.letterSpacing = '0.01em';
                el.style.fontWeight = '450';
                el.style.lineHeight = '1.65';

                // Apply to message content inside
                const content = el.querySelector('.message-content');
                if (content) {
                    content.style.background = '#f8f9fa';
                    content.style.color = '#333333';
                    content.style.borderLeft = 'none';
                    content.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif';
                    content.style.fontSize = '0.95rem';
                    content.style.letterSpacing = '0.01em';
                    content.style.fontWeight = '450';
                    content.style.lineHeight = '1.65';
                }
            });
        }

        // Apply styles immediately
        applyMessageStyles();

        // Set up a mutation observer to apply styles to new messages
        const chatBox = document.getElementById('chat-box');
        if (chatBox) {
            const observer = new MutationObserver(function(mutations) {
                applyMessageStyles();
            });

            observer.observe(chatBox, { childList: true, subtree: true });
        }
    });
</script>


{% endblock extra_head %}

{% block content %}
<!-- Inline styles to ensure chat bubbles are styled correctly -->
<style>
.message.assistant-message {
    background: transparent !important;
}

.message.assistant-message .message-content {
    background: linear-gradient(135deg, #ffffff 0%, var(--primary-light) 100%) !important;
    color: #333333 !important;
    border-color: rgba(13, 110, 253, 0.15) !important;
    max-width: 75% !important;
    font-weight: 450 !important;
    font-size: 1rem !important;
    line-height: 1.6 !important;
    letter-spacing: 0.01em !important;
}

.message.user-message {
    background: transparent !important;
}

.message.user-message .message-content {
    background: linear-gradient(135deg, #f8f9fa 0%, #e6e6e6 100%) !important;
    color: #333333 !important;
    border-color: rgba(0, 0, 0, 0.05) !important;
    max-width: 75% !important;
}

.message-content {
    border-left: none !important;
}

/* Tab styling */
.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    color: #6c757d;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-size: 0.95rem;
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    transition: all 0.2s ease;
    margin-right: 0.25rem;
}

.nav-tabs .nav-link:hover {
    border-color: rgba(0, 0, 0, 0.05);
    color: #495057;
    background-color: rgba(0, 0, 0, 0.01);
}

.nav-tabs .nav-link.active {
    background-color: #fff;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) #fff;
    color: #333333;
    font-weight: 600;
    border-bottom: 2px solid #0d6efd;
}

/* Community tabs specific styling */
#communityTabs {
    position: relative;
    z-index: 10;
    display: flex !important; /* Force display flex to ensure tabs are always visible */
    justify-content: flex-start;
    background-color: #ffffff;
    border-radius: 0.7rem 0.7rem 0 0;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-bottom: none;
    padding: 0.5rem 0.5rem 0 0.5rem;
    max-width: 800px;
    margin: 0 auto;
    visibility: visible !important; /* Ensure tabs are always visible */
}

/* Enhanced Sidebar styling */
#chat-sidebar {
    position: fixed;
    top: 56px; /* Start below the header */
    left: -350px;
    width: 300px;
    max-width: 80%;
    height: calc(100vh - 56px); /* Adjust height to account for header */
    background-color: #ffffff;
    border-radius: 0 0.8rem 0.8rem 0;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 1.5rem 1rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    z-index: 1080; /* Higher z-index to ensure it's above everything */
    transition: left 0.3s ease, box-shadow 0.3s ease;
    overflow-y: auto;
    transform: translateZ(0); /* Force hardware acceleration */
}

#chat-sidebar.active {
    left: 0;
    box-shadow: 0 0 25px rgba(0, 0, 0, 0.15);
}

/* Style the sidebar navigation items */
#chat-sidebar .list-group-item {
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

/* Add shine effect on hover */
#chat-sidebar .list-group-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease-in-out;
}

#chat-sidebar .list-group-item:hover::before {
    left: 100%;
}

#chat-sidebar .list-group-item:hover {
    background-color: #f0f7ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
    border-left: 3px solid #0d6efd;
    color: #0d6efd;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

#chat-sidebar .list-group-item:hover .bi-chevron-right {
    transform: translateX(3px);
    color: #0d6efd;
}

#chat-sidebar .list-group-item.active {
    background-color: #e6f0fd;
    color: #0d6efd;
    border-left: 3px solid #0d6efd;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

#chat-sidebar .list-group-item.active .bi-chevron-right {
    transform: translateX(3px);
    color: #0d6efd;
}

#sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    z-index: 1070; /* Just below sidebar */
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

#sidebar-overlay.active {
    display: block;
    opacity: 1;
}

/* Community chat styling */
.community-chat-box {
    margin-top: 0 !important;
    border-radius: 0 0 0.7rem 0.7rem !important;
}

.general-chat-box {
    border-radius: 0.7rem !important;
}

.community-container {
    border-radius: 0 0 0.8rem 0.8rem !important;
}

.general-chat-container {
    border-radius: 0.8rem !important;
}

/* Chat container and tabs styling */
.chat-container {
    max-width: 1200px; /* Increased from 800px to 1200px */
    margin: 0 auto;
    width: 100%;
}

#communityTabs {
    max-width: 1200px; /* Increased from 800px to 1200px */
    margin: 0 auto;
    width: 100%;
}

/* Ensure the tab content doesn't shift when sidebar is toggled */
.tab-content {
    width: 100%;
    max-width: 100%;
    transition: none !important;
}

/* Ensure tab panes are properly displayed */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block !important;
}

.tab-pane.show.active {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Tab hover effects */
.nav-tabs .nav-link {
    position: relative;
    overflow: hidden;
}

.nav-tabs .nav-link:hover {
    background-color: #f0f7ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    color: #0d6efd !important;
    transition: all 0.2s ease-in-out;
}

.nav-tabs .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: #0d6efd;
    transition: all 0.3s ease-in-out;
}

.nav-tabs .nav-link:hover::before {
    width: 100%;
    left: 0;
}

.nav-tabs .nav-link.active::before {
    width: 100%;
    left: 0;
}
</style>



<div class="container-fluid"> {# Removed top margin to maximize space #}

    <div class="row position-relative">
        {# Sidebar - Conditionally hidden for 'general' type or if show_sidebar is False #}
        {% if assistant.assistant_type != 'general' and assistant.show_sidebar %}
        <div id="chat-sidebar" style="position: fixed; top: 56px; left: -350px; width: 80%; max-width: 300px; height: calc(100vh - 56px); z-index: 1080; background-color: #fff; overflow-y: auto; transition: left 0.3s ease; box-shadow: 0 0 15px rgba(0, 0, 0, 0.1); padding: 1rem; transform: translateZ(0); display: block; visibility: visible; opacity: 1;">
            {# Sidebar Toggle Button (inside sidebar) #}
            <div class="d-flex align-items-center flex-wrap mb-2"> {# Flex container with bottom margin #}
                <button type="button" id="sidebar-toggle-inside" class="btn btn-success btn-sm" title="Toggle Sidebar" style="box-shadow: 0 2px 5px rgba(0,0,0,0.3); cursor: pointer; font-weight: bold; padding: 6px 10px; border-radius: 5px; width: 100%;" onclick="var sidebar = document.getElementById('chat-sidebar'); var overlay = document.getElementById('sidebar-overlay'); if(sidebar){sidebar.classList.remove('active');sidebar.style.left='-350px';if(overlay){overlay.style.opacity='0';overlay.style.pointerEvents='none';setTimeout(()=>{overlay.style.display='none';},300);}}">
                    <i class="bi bi-list me-1"></i> <span class="toggle-text" style="padding: 2px 5px; border-radius: 3px; font-weight: bold;">Close Menu</span>
                </button>
            </div> {# End flex container #}
            <hr style="margin: 0.75rem 0; border-color: rgba(0, 0, 0, 0.05);">
            <h5>Navigation</h5>
            <div class="list-group list-group-flush"> {# Added list-group-flush for potentially better styling #}
                {# Loop through NavigationItem objects ordered by 'order' from the view #}
                {% for item in navigation_items %}
                    {# Visibility is already filtered in the view query #}
                    {# Use item.unique_id if available and reliable, otherwise fallback to pk #}
                    <button type="button" class="list-group-item list-group-item-action nav-button" data-section-id="{{ item.unique_id|default:item.pk }}">
                        {{ item.label }}
                    </button>
                {% empty %}
                     <span class="list-group-item text-muted">No navigation items configured.</span>
                {% endfor %}
            </div>
        </div>
        {% endif %}



        {# Main Chat Area - Always full width so sidebar can overlay #}
        <div class="col-12"> {# Always full width #}
            {# Sidebar Toggle Button (Desktop - green test button) - Only show if sidebar is enabled #}
            {% if assistant.assistant_type != 'general' and assistant.show_sidebar %}
            <button type="button" id="test-sidebar-toggle" class="btn btn-success btn-sm position-absolute top-0 start-0 m-3 d-none d-md-inline-block" title="Toggle Sidebar" style="z-index: 1075; box-shadow: 0 4px 8px rgba(0,0,0,0.3); border-radius: 0.5rem; cursor: pointer; font-weight: bold; padding: 8px 15px; display: block;" onclick="var sidebar = document.getElementById('chat-sidebar'); if(sidebar){sidebar.style.display='block';sidebar.style.visibility='visible';sidebar.classList.toggle('active');sidebar.style.left=sidebar.classList.contains('active')?'0':'-350px';}">
                <i class="bi bi-list me-1"></i> <span class="toggle-text" style="padding: 3px 6px; border-radius: 3px; font-weight: bold;">Menu</span>
            </button>
            {% endif %}

            {# Header block removed to prevent duplication #}
            {# End Removed Info Header #}

            {% if assistant.assistant_type == 'community' %}
            <!-- Community Assistant Tabs - Above tab content -->
            <ul class="nav nav-tabs mb-2" id="communityTabs" role="tablist" style="border-bottom: none; background-color: #ffffff; border-radius: 0.7rem 0.7rem 0 0; padding: 0.5rem 0.5rem 0 0.5rem; border: 1px solid rgba(0, 0, 0, 0.05); border-bottom: none; width: 100%; max-width: 800px; margin: 0 auto 0 auto;">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="chat-tab" data-bs-toggle="tab" data-bs-target="#chat-pane" type="button" role="tab" aria-controls="chat-pane" aria-selected="true" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 0.95rem; font-weight: 500; color: #333333; padding: 0.75rem 1.25rem; border-radius: 0.25rem 0.25rem 0 0; transition: all 0.2s ease-in-out;">
                        <i class="bi bi-chat-dots me-1"></i> Chat
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contributions-tab" data-bs-toggle="tab" data-bs-target="#contributions-pane" type="button" role="tab" aria-controls="contributions-pane" aria-selected="false" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 0.95rem; font-weight: 500; color: #6c757d; padding: 0.75rem 1.25rem; border-radius: 0.25rem 0.25rem 0 0; transition: all 0.2s ease-in-out;">
                        <i class="bi bi-collection me-1"></i> Contributions
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="assist-tab" data-bs-toggle="tab" data-bs-target="#assist-pane" type="button" role="tab" aria-controls="assist-pane" aria-selected="false" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 0.95rem; font-weight: 500; color: #6c757d; padding: 0.75rem 1.25rem; border-radius: 0.25rem 0.25rem 0 0; transition: all 0.2s ease-in-out;">
                        <i class="bi bi-flag me-1"></i> Assist
                    </button>
                </li>
                <li class="nav-item ms-auto" role="presentation">
                    <a href="{% url 'assistants:community_dashboard' assistant.company.id assistant.id %}" class="nav-link" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 0.95rem; font-weight: 500; color: #0d6efd; padding: 0.75rem 1.25rem; border-radius: 0.25rem 0.25rem 0 0; transition: all 0.2s ease-in-out;">
                        <i class="bi bi-people me-1"></i> Social Dashboard
                    </a>
                </li>
            </ul>
            {% endif %}
            <div class="tab-content" id="communityTabContent" style="width: 100%; position: relative; z-index: 5;">
                <!-- Chat Tab Pane -->
                <div class="tab-pane fade show active" id="chat-pane" role="tabpanel" aria-labelledby="chat-tab">
                    <div class="chat-container {% if assistant.assistant_type == 'general' %}general-chat-container{% else %}mt-0{% endif %} {% if assistant.assistant_type == 'community' %}community-container{% endif %}" style="background-color: #FFFFFF !important; background: #FFFFFF !important; background-image: none !important; padding: 0; border: 1px solid #dee2e6; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); max-width: 1200px; margin: 0 auto; position: relative; top: 0; border-radius: 1.25rem;">
                        <div class="chat-box {% if assistant.assistant_type == 'general' %}general-chat-box{% endif %}{% if assistant.assistant_type == 'community' %} community-chat-box{% endif %}" id="chat-box" style="position: relative; top: 0; background-color: #FFFFFF !important; background: #FFFFFF !important; background-image: none !important; padding: 1.75rem; border: 1px solid #dee2e6; margin-bottom: 0.5rem; border-radius: 1.25rem;">
                            {# Initial Display Area: Logo + Welcome Message #}
                            <div id="initial-display-area" class="text-center mb-4 {% if assistant.assistant_type == 'community' %}community-initial-display{% endif %}" style="display: block !important; visibility: visible !important; opacity: 1 !important; height: auto !important; overflow: visible !important; margin-bottom: 1rem !important; padding: 0 !important;"> {# Visible by default #}
                                <script>
                                    // Immediate inline script to hide this element if there are messages
                                    (function() {
                                        // Check for existing messages in the DOM
                                        const existingMessages = document.querySelectorAll('.message:not(.initial-greeting)');
                                        const userMessages = document.querySelectorAll('.user-message');

                                        // Hide welcome message if there are any messages, especially user messages
                                        if (existingMessages.length > 0 || userMessages.length > 0) {
                                            const initialDisplayArea = document.getElementById('initial-display-area');
                                            if (initialDisplayArea) {
                                                initialDisplayArea.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; height: 0 !important; overflow: hidden !important; margin: 0 !important; padding: 0 !important;';
                                                console.log('Initial display area hidden by inline script in display area');
                                            }
                                        }

                                        // Also check session storage for chat history
                                        const assistantId = document.querySelector('meta[name="assistant-id"]')?.content || 'default';
                                        const sessionStorageKey = `chatHistory_${assistantId}`;
                                        const storedHistory = sessionStorage.getItem(sessionStorageKey);

                                        if (storedHistory) {
                                            try {
                                                const history = JSON.parse(storedHistory);
                                                if (Array.isArray(history) && history.length > 0) {
                                                    // Check if there's at least one user message in the history
                                                    const hasUserMessage = history.some(msg => msg.role === 'user');

                                                    if (hasUserMessage) {
                                                        const initialDisplayArea = document.getElementById('initial-display-area');
                                                        if (initialDisplayArea) {
                                                            initialDisplayArea.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; height: 0 !important; overflow: hidden !important; margin: 0 !important; padding: 0 !important;';
                                                        }
                                                    }
                                                }
                                            } catch (e) {
                                                console.error('Failed to parse chat history:', e);
                                            }
                                        }
                                    })();
                                </script>
                                {# Display Avatar: Assistant Avatar > Default Avatar > Assistant Logo > Placeholder #}
                                {% if assistant.avatar and assistant.avatar.url %}
                                    <img src="{{ assistant.avatar.url }}" alt="{{ assistant.persona_name|default:assistant.name }} Avatar" class="assistant-profile-pic mb-3 {% if assistant.assistant_type == 'general' %}general-avatar{% endif %}" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important; width: 190px; height: 190px; border-radius: 50%; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border: 4px solid rgba(255, 255, 255, 0.2); object-fit: cover;" loading="eager" fetchpriority="high" decoding="async">
                                {% elif default_logo_url %}
                                    <img src="{{ default_logo_url }}" alt="Default Assistant Avatar" class="assistant-profile-pic mb-3 {% if assistant.assistant_type == 'general' %}general-avatar{% endif %}" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important; width: 190px; height: 190px; border-radius: 50%; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border: 4px solid rgba(255, 255, 255, 0.2); object-fit: cover;" loading="eager" fetchpriority="high" decoding="async">
                                {% else %}
                                    {% with logo_url=assistant.get_logo_url %}
                                        {% if logo_url %}
                                            <img src="{{ logo_url }}" alt="{{ assistant.persona_name|default:assistant.name }} Logo" class="assistant-profile-pic mb-3 {% if assistant.assistant_type == 'general' %}general-avatar{% endif %}" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important; width: 190px; height: 190px; border-radius: 50%; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border: 4px solid rgba(255, 255, 255, 0.2); object-fit: cover;" loading="eager" fetchpriority="high" decoding="async">
                                        {% else %}
                                            <i class="bi bi-person-circle assistant-profile-pic-placeholder text-muted mb-3 {% if assistant.assistant_type == 'general' %}general-avatar-placeholder{% endif %}" style="display: inline-block !important; visibility: visible !important; opacity: 1 !important; font-size: 190px; text-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);"></i>
                                        {% endif %}
                                    {% endwith %}
                                {% endif %}

                                {# Navigation Section Content Block - Hidden by default, will be shown when nav items are clicked #}
                                <div id="nav-section-content" class="mt-3" style="display: none;">
                                    <!-- Debug: Website Data Keys: {{ website_data.keys|join:", " }} -->
                                    {% if navigation_items %}
                                        {% for item in navigation_items %}
                                            <div class="nav-section-content-block" id="nav-section-{{ item.unique_id }}" style="display: none;" data-nav-label="{{ item.label }}">
                                                {# Try to get content using different methods #}
                                                {% with item_key='item_'|add:item.id|stringformat:"s" %}
                                                    <!-- Debug info: Item ID: {{ item.id }}, Item Key: {{ item_key }}, Unique ID: {{ item.unique_id }} -->
                                                    {% if website_data|get_nav_data:item_key %}
                                                        {# Method 1: Try item_ID format #}
                                                        {% with item_content=website_data|get_nav_data:item_key %}
                                                            <!-- Found content using item_key: {{ item_key }} -->
                                                            {% if item_content.content %}
                                                                {{ item_content.content|safe }}
                                                            {% else %}
                                                                {{ item_content|safe }}
                                                            {% endif %}
                                                        {% endwith %}
                                                    {% elif website_data|get_nav_data:item.unique_id %}
                                                        {# Method 2: Try unique_id format #}
                                                        {% with item_content=website_data|get_nav_data:item.unique_id %}
                                                            <!-- Found content using unique_id: {{ item.unique_id }} -->
                                                            {% if item_content.content %}
                                                                {{ item_content.content|safe }}
                                                            {% else %}
                                                                {{ item_content|safe }}
                                                            {% endif %}
                                                        {% endwith %}
                                                    {% else %}
                                                        <!-- No content found for item_key: {{ item_key }} or unique_id: {{ item.unique_id }} -->
                                                        {# Fallback: Show better placeholder content #}
                                                        <h3>{{ item.label }}</h3>
                                                        <p>Welcome to the {{ item.label }} section.</p>
                                                        <p>This section contains important information about {{ item.label }}.</p>
                                                        <p>Please click on this section in the sidebar to view the full content.</p>
                                                    {% endif %}
                                                {% endwith %}
                                            </div>
                                        {% endfor %}
                                    {% endif %}
                                </div>

                                {# Initial welcome message - Use custom greeting or default #}
                                <div class="initial-greeting welcome-text-small {% if assistant.assistant_type == 'general' %}general-welcome-text{% endif %}" style="display: block !important; background: transparent !important; color: #ffffff !important; text-align: center !important; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif !important; font-size: 1.9rem !important; letter-spacing: 0.01em !important; font-weight: bold !important; line-height: 1.65 !important; margin: 1rem 0 !important; padding: 0 !important; border: none !important; box-shadow: none !important;">
                                    {% if assistant.greeting_message and assistant.greeting_message|length > 0 %}
                                        {{ assistant.greeting_message|safe }} {# Use safe to render HTML in greeting #}
                                    {% else %}
                                        Hello! I'm {{ assistant.persona_name|default:assistant.name }}, your assistant for {{ assistant.company.name }}. How can I help you today?
                                    {% endif %}
                                </div>
                            </div>
                            {# End Initial Display Area #}
                            {# Chat messages will be loaded here dynamically #}
                        </div>

                        {# Suggestions Area - REMOVED COMPLETELY #}
                    </div> {# Closes chat-container #}
                </div>

                {% if assistant.assistant_type == 'community' %}
                <!-- Contributions Tab Pane -->
                <div class="tab-pane fade" id="contributions-pane" role="tabpanel" aria-labelledby="contributions-tab" style="max-width: 800px; margin: 0 auto;">
                    <div class="alert alert-info mb-3" style="background-color: #f0f7ff; border-color: rgba(13, 110, 253, 0.1); color: #333333; border-radius: 0.7rem; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 0.95rem; font-weight: 450; letter-spacing: 0.01em;">
                        <i class="bi bi-info-circle me-2"></i>
                        Contribute knowledge to help the community assistant provide better answers
                    </div>
                    <div class="card border-0 mb-4" style="background-color: #f8f9fa; border-radius: 0.8rem; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);">
                        <div class="card-body p-4" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;">
                            <h4 class="h5 mb-3">Add New Knowledge</h4>
                            <form action="{% url 'assistants:add_context' assistant.company.id assistant.id %}" method="post" class="mb-3" enctype="multipart/form-data" id="knowledge-form" onsubmit="if(typeof tinymce !== 'undefined' && tinymce.get('id_text_content')) { tinymce.get('id_text_content').save(); }">
                                <!-- This form uses django-tinymce widget -->
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="id_title" class="form-label" style="font-size: 0.95rem; font-weight: 500; color: #333333;">Title</label>
                                    <input type="text" name="title" class="form-control" id="id_title" placeholder="Give your contribution a title" style="border-radius: 0.5rem; border-color: rgba(0, 0, 0, 0.1); padding: 0.6rem 0.8rem; font-size: 0.95rem; background-color: #ffffff;">
                                </div>
                                <div class="mb-3">
                                    <label for="id_text_content" class="form-label" style="font-size: 0.95rem; font-weight: 500; color: #333333;">Knowledge Content</label>
                                    <textarea name="text_content" class="form-control tinymce-editor" id="id_text_content" rows="10" placeholder="Share your knowledge here..." style="border-radius: 0.5rem; border-color: rgba(0, 0, 0, 0.1); padding: 0.6rem 0.8rem; font-size: 0.95rem; background-color: #ffffff;"></textarea>

                                    <!-- Fallback textarea that will be shown if TinyMCE fails to initialize -->
                                    <div id="tinymce-fallback" style="display:none; margin-top:10px;">
                                        <div class="alert alert-warning">
                                            <strong>Note:</strong> The rich text editor failed to load. You can still enter content below:
                                        </div>
                                        <textarea name="fallback_content" class="form-control" rows="10" placeholder="Enter your content here if the rich text editor doesn't appear..."></textarea>
                                    </div>

                                    <script>
                                        // Check if TinyMCE is loaded after a delay
                                        setTimeout(function() {
                                            if (typeof tinymce === 'undefined' || !tinymce.get('id_text_content')) {
                                                // Show fallback textarea
                                                document.getElementById('tinymce-fallback').style.display = 'block';
                                            }

                                            // Clean up any duplicate editor containers
                                            if (typeof window.cleanupTinyMCEContainers === 'function') {
                                                window.cleanupTinyMCEContainers();
                                            }
                                        }, 2000);

                                        // Also clean up when the contributions tab is clicked
                                        document.addEventListener('DOMContentLoaded', function() {
                                            const contributionsTab = document.getElementById('contributions-tab');
                                            if (contributionsTab) {
                                                contributionsTab.addEventListener('click', function() {
                                                    setTimeout(function() {
                                                        if (typeof window.cleanupTinyMCEContainers === 'function') {
                                                            window.cleanupTinyMCEContainers();
                                                        }
                                                    }, 500);
                                                });
                                            }
                                        });
                                    </script>
                                </div>
                                <div class="d-grid mt-3">
                                    <button type="submit" class="btn btn-primary" style="border-radius: 0.5rem; padding: 0.6rem 1rem; font-size: 0.95rem; font-weight: 500; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; letter-spacing: 0.01em; box-shadow: 0 1px 3px rgba(13, 110, 253, 0.2);">
                                        <i class="bi bi-plus-circle me-2"></i> Add Knowledge
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Recent Contributions -->
                    <h4 class="h5 mb-3" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 1rem; font-weight: 500; color: #333333;">Recent Contributions</h4>
                    <div id="recent-contexts-container" style="background-color: #ffffff; border-radius: 0.8rem; border: 1px solid rgba(0, 0, 0, 0.05); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03); padding: 1rem;">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 0.95rem; color: #6c757d;">Loading recent contributions...</p>
                        </div>
                    </div>
                </div>

                <!-- Assist Tab Pane -->
                <div class="tab-pane fade" id="assist-pane" role="tabpanel" aria-labelledby="assist-tab" style="max-width: 800px; margin: 0 auto;">
                    <div class="card mb-4" style="background-color: #f8f9fa; border-radius: 0.8rem; border: 1px solid rgba(0, 0, 0, 0.05); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);">
                        <div class="card-header" style="background-color: #f0f7ff; border-bottom: 1px solid rgba(0, 0, 0, 0.05); border-radius: 0.8rem 0.8rem 0 0; padding: 1rem;">
                            <h5 class="card-title mb-0" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; font-size: 1rem; font-weight: 500; color: #333333;">Questions Needing Better Answers</h5>
                        </div>
                        <div class="card-body p-0" id="flagged-questions-container" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading questions that need assistance...</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div> {# Closes col-md-9 or col-12 #}
    </div> {# Closes row #}



</div> {# Closes container-fluid #}

{# Fixed Chat Input Form at Bottom of Page #}
<div id="fixed-chat-input" class="fixed-bottom bg-white border-top shadow-lg">
    <div class="container-fluid p-3">
        <div class="row align-items-center">
            {# Reset Button - Shorter text on mobile #}
            <div class="col-auto">
                <button type="button" id="reset-chat-btn" class="btn btn-outline-secondary btn-sm" title="Reset Conversation">
                    <span class="d-none d-md-inline">Reset Conversation</span>
                    <span class="d-md-none">Reset</span>
                </button>
            </div>

            {# Input and Send Button #}
            <div class="col">
                <div class="row g-2">
                    <div class="col">
                        <input type="text" id="message-input" class="form-control" placeholder="Ask anything..." autocomplete="off" style="border-radius: 1.5rem; padding: 0.75rem 1rem;">
                    </div>
                    <div class="col-auto">
                        <button type="button" id="send-button" class="btn btn-primary" style="border-radius: 1.5rem; padding: 0.75rem 1rem;">
                            {# Show icon on both mobile and desktop for consistency #}
                            <i class="bi bi-send-fill"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {# Hidden form for CSRF token #}
        <form id="chat-form" class="d-none">
            {% csrf_token %}
        </form>
    </div>
</div>

{# Modals defined outside the main row/column structure but inside the content block #}

<!-- Context Modal -->
<div class="modal fade" id="contextModal" tabindex="-1" aria-labelledby="contextModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contextModalLabel">Source Context</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="contextContent" class="context-content"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Gallery Modal (for section galleries) -->
<div class="modal fade" id="sectionGalleryModal" tabindex="-1" aria-labelledby="sectionGalleryModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="sectionGalleryModalLabel">Gallery</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center" id="sectionGalleryModalBody">
        <!-- Gallery images will be injected here by JS -->
      </div>
    </div>
  </div>
</div>

{# Include Rating Modal Structure (same as in assistant_list.html) #}
{# Ideally, this would be in a base template or an include #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Select your rating for <strong id="modalAssistantName">this assistant</strong>:</p>
        <div class="modal-stars text-center mb-3" style="font-size: 2rem;"> {# Larger stars in modal #}
            {% for i_int in "12345" %}
            <button class="modal-star-btn btn btn-link text-secondary p-1" data-rating-value="{{ i_int }}" title="Rate {{ i_int }} star{{ i_int|pluralize }}">
                <i class="bi bi-star"></i>
            </button>
            {% endfor %}
        </div>
        <div id="modalErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
      </div>
    </div>
  </div>
</div>

<script>
// Fix for sidebar overlay when rating modal is closed
document.addEventListener('DOMContentLoaded', function() {
    const ratingModal = document.getElementById('ratingModal');
    if (ratingModal) {
        ratingModal.addEventListener('hidden.bs.modal', function() {
            // No sidebar overlay to hide
        });
    }
});
</script>
{# End Rating Modal #}

{# QR Code Modal #}
<div class="modal fade" id="qrCodeModal" tabindex="-1" aria-labelledby="qrCodeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-sm modal-dialog-centered"> {# Smaller modal #}
    <div class="modal-content">
      <div class="modal-header">
         <h5 class="modal-title" id="qrCodeModalLabel">Scan QR Code</h5>
         <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
       </div>
       <div class="modal-body d-flex flex-column align-items-center justify-content-center p-4">
         {# Centered QR code with improved styling #}
         <div class="qr-code-container mb-3 p-2 bg-white rounded shadow-sm" style="width: 220px; height: 220px; display: flex; align-items: center; justify-content: center;">
           <img src="" id="qrCodeImage" class="img-fluid" alt="QR Code for Chat" style="max-width: 200px; max-height: 200px;">
         </div>
         {# Add download link below the image #}
         <a href="#" id="qrCodeDownloadLink" class="btn btn-sm btn-outline-primary mt-2 mb-2" download="assistant_qr_code.png">
             <i class="bi bi-download me-1"></i> Download QR Code
         </a>
         <p class="text-center small" id="qrCodeDescription">Scan this code to open the chat.</p>
       </div>
     </div>
  </div>
</div>

<script>
// Fix for sidebar overlay when QR code modal is closed
document.addEventListener('DOMContentLoaded', function() {
    const qrCodeModal = document.getElementById('qrCodeModal');
    if (qrCodeModal) {
        qrCodeModal.addEventListener('hidden.bs.modal', function() {
            // No sidebar overlay to hide
        });
    }
});
</script>
{# End QR Code Modal #}

{# Image Modal (for single image view) - Add this if not already present #}
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="imageModalLabel">Image Preview</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center">
        <img src="" id="modalImage" class="img-fluid" alt="Preview">
      </div>
    </div>
  </div>
</div>

<script>
// Fix for sidebar overlay when image modal is closed
document.addEventListener('DOMContentLoaded', function() {
    const imageModal = document.getElementById('imageModal');
    if (imageModal) {
        imageModal.addEventListener('hidden.bs.modal', function() {
            // No sidebar overlay to hide
        });
    }
});
</script>
{# End Image Modal #}

{# Context Modal (for viewing community context) #}
<div class="modal fade" id="contextModal" tabindex="-1" aria-labelledby="contextModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="contextModalLabel">Context</h5>
        <div class="context-metadata ms-3 text-muted"></div>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <!-- Context content will be loaded here -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<script>
// Fix for sidebar overlay when context modal is closed
document.addEventListener('DOMContentLoaded', function() {
    const contextModal = document.getElementById('contextModal');
    if (contextModal) {
        contextModal.addEventListener('hidden.bs.modal', function() {
            // No sidebar overlay to hide
        });
    }
});
</script>
{# End Context Modal #}



<script>
    // Function to get CSRF token from cookies
    function getCsrfToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Function to handle like button clicks
    document.addEventListener('DOMContentLoaded', function() {
        // Set up like button functionality
        const likeButtons = document.querySelectorAll('.like-button');
        likeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const itemId = this.getAttribute('data-item-id');
                const itemType = this.getAttribute('data-item-type');
                const icon = this.querySelector('i');
                const isFavorited = icon.classList.contains('bi-heart-fill');

                // Get CSRF token
                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    console.error('CSRF token not found');
                    return;
                }

                // Send request to toggle saved status
                fetch('/directory/toggle_saved_item/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({
                        'item_id': itemId,
                        'item_type': itemType
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        // Update button appearance
                        if (data.action === 'unfavorited') {
                            // Item was unfavorited
                            icon.classList.remove('bi-heart-fill', 'text-danger');
                            icon.classList.add('bi-heart');
                            this.textContent = '';
                            this.appendChild(icon);
                            this.appendChild(document.createTextNode(' Favorite'));
                            this.title = 'Add to Favorites';
                        } else {
                            // Item was favorited (any other success action)
                            icon.classList.remove('bi-heart');
                            icon.classList.add('bi-heart-fill', 'text-danger');
                            this.textContent = '';
                            this.appendChild(icon);
                            this.appendChild(document.createTextNode(' Favorited'));
                            this.title = 'Remove from Favorites';
                        }
                    } else if (data.status === 'options') {
                        // Item is not saved, save it directly without showing folder options
                        // Send another request to save without folder
                        fetch('/directory/save_without_folder/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                                'X-CSRFToken': csrfToken,
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: new URLSearchParams({
                                'item_id': itemId,
                                'item_type': itemType
                            })
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(saveData => {
                            if (saveData.status === 'success') {
                                // Update button appearance
                                icon.classList.remove('bi-heart');
                                icon.classList.add('bi-heart-fill', 'text-danger');
                                this.textContent = '';
                                this.appendChild(icon);
                                this.appendChild(document.createTextNode(' Favorited'));
                                this.title = 'Remove from Favorites';
                            } else {
                                console.error('Error saving item:', saveData.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error saving item:', error);
                        });
                    } else {
                        console.error('Error toggling saved status:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        });
    });

    // Function to toggle sidebar visibility - now implemented in button-functionality.js
    function toggleSidebar() {
        // This is just a stub that calls the implementation in button-functionality.js
        if (typeof window.toggleSidebar === 'function') {
            window.toggleSidebar();
        } else {
            console.log('Sidebar toggle function not found in button-functionality.js');
        }
    }

    // Sidebar toggle event listeners are now handled in button-functionality.js
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Sidebar toggle event listeners are now handled in button-functionality.js');
    });
</script>

{# Flag Answer Modal for Users #}
<div class="modal fade" id="flagAnswerModal" tabindex="-1" aria-labelledby="flagAnswerModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="flagAnswerModalLabel">Flag This Answer</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Help improve the assistant by flagging answers that need community input.</p>

        <div class="card mb-3 border-light bg-light">
          <div class="card-body">
            <h6 class="card-subtitle mb-2 text-muted">Original Question:</h6>
            <p class="card-text" id="modal-question-display"></p>
            <h6 class="card-subtitle mb-2 text-muted">Current Answer:</h6>
            <p class="card-text" id="modal-answer-display"></p>
          </div>
        </div>

        <form id="flag-answer-form">
          <input type="hidden" id="flag-question" name="question" value="">
          <input type="hidden" id="flag-answer" name="answer" value="">

          <div class="mb-3">
            <label for="flag-reason" class="form-label">Why is this answer unsatisfactory?</label>
            <select class="form-select" id="flag-reason" name="reason" required>
              <option value="">Select a reason...</option>
              <option value="incorrect">Information is incorrect</option>
              <option value="incomplete">Answer is incomplete</option>
              <option value="unclear">Answer is unclear or confusing</option>
              <option value="outdated">Information is outdated</option>
              <option value="other">Other reason</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="flag-email" class="form-label">Email (optional):</label>
            <input type="email" class="form-control" id="flag-email" name="email" placeholder="<EMAIL>">
            <div class="form-text">
              <i class="bi bi-info-circle me-1"></i>
              Provide your email to receive notifications when community members add context.
            </div>
          </div>
        </form>

        <div class="alert alert-info mt-3">
          <i class="bi bi-people-fill me-2"></i>
          After flagging, community members can add their knowledge to improve this answer.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-warning" id="submit-flag-btn">
          <i class="bi bi-flag me-1"></i> Submit Flag
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Fix for sidebar overlay when flag answer modal is closed
document.addEventListener('DOMContentLoaded', function() {
    const flagAnswerModal = document.getElementById('flagAnswerModal');
    if (flagAnswerModal) {
        flagAnswerModal.addEventListener('hidden.bs.modal', function() {
            // Hide sidebar overlay when flag answer modal is closed
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            if (sidebarOverlay) {
                sidebarOverlay.style.opacity = '0';
                sidebarOverlay.style.pointerEvents = 'none';
                setTimeout(() => {
                    sidebarOverlay.style.display = 'none';
                }, 300);
            }
        });
    }
});
</script>
{# End Flag Answer Modal #}



{% block extra_js %}
<script>
    // General fix for sidebar overlay when any modal is closed
    document.addEventListener('DOMContentLoaded', function() {
        // This will handle any modals added in the future
        document.body.addEventListener('hidden.bs.modal', function(event) {
            if (event.target.classList.contains('modal')) {
                // No sidebar overlay to hide
            }
        });
    });

     // --- Define constants and functions in the outer scope ---
     // Get assistant ID and company ID directly from template variables to avoid errors
     const assistantId = "{{ assistant.id }}";
     const companyId = "{{ assistant.company.id }}";
     const assistantType = "{{ assistant.assistant_type }}"; // Add assistant type
     const sessionStorageKey = `chatHistory_${assistantId}`;
     let initialDisplayAreaHTML = ''; // Variable to store initial display area HTML
     let currentNavContext = ''; // Variable to store context from last nav click
     let lastQuestion = ''; // Variable to store the last question asked

     // Global flag to track if TinyMCE has been initialized for the contributions textarea
     window.tinyMCEInitialized = window.tinyMCEInitialized || false;



     // Function to show context modal
     function showContextModal(contextId, contextTitle, createdBy, createdAt) {
         // Get the modal element
         const contextModal = document.getElementById('contextModal');
         if (!contextModal) return;

         // Set the modal title
         const modalTitle = contextModal.querySelector('.modal-title');
         if (modalTitle) modalTitle.textContent = contextTitle;

         // Set the modal metadata
         const modalMetadata = contextModal.querySelector('.context-metadata');
         if (modalMetadata) modalMetadata.innerHTML = `<small>Created by: ${createdBy} on ${createdAt}</small>`;

         // Show loading spinner
         const modalBody = contextModal.querySelector('.modal-body');
         if (modalBody) {
             modalBody.innerHTML = `
                 <div class="text-center py-4">
                     <div class="spinner-border text-primary" role="status">
                         <span class="visually-hidden">Loading...</span>
                     </div>
                     <p class="mt-2">Loading context content...</p>
                 </div>
             `;
         }

         // Show the modal
         const modal = new bootstrap.Modal(contextModal);
         modal.show();

         // Fetch the context content
         fetch(`/assistant/company/${companyId}/assistants/${assistantId}/context/${contextId}/`)
             .then(response => {
                 if (!response.ok) {
                     throw new Error('Network response was not ok');
                 }
                 return response.json();
             })
             .then(data => {
                 if (modalBody) {
                     modalBody.innerHTML = `<div class="context-content">${data.content}</div>`;
                 }
             })
             .catch(error => {
                 if (modalBody) {
                     modalBody.innerHTML = `
                         <div class="alert alert-danger">
                             <i class="bi bi-exclamation-triangle-fill me-2"></i>
                             Error loading context: ${error.message}
                         </div>
                     `;
                 }
             });
     }

     // Function to display gallery in a modal
     function showGalleryModal(galleryUrls) {
         const modalBody = document.getElementById('sectionGalleryModalBody');
         if (!modalBody) return;

         modalBody.innerHTML = ''; // Clear existing content
         galleryUrls.forEach(imageUrl => {
             const img = document.createElement('img');
             img.src = imageUrl;
             img.classList.add('img-fluid', 'm-2');
             img.style.maxHeight = '300px';
             img.style.borderRadius = '6px';
 	        img.style.cursor = 'pointer';
             img.onclick = function() {
                 openImageModal(imageUrl); // Open single image modal on click
             };
             modalBody.appendChild(img);
         });

         const galleryModal = new bootstrap.Modal(document.getElementById('sectionGalleryModal'));
         galleryModal.show();
     }

    // Function to open single image modal
    function openImageModal(imageSrc) {
        const modalImage = document.getElementById('modalImage');
        if (modalImage) {
            modalImage.src = imageSrc;
            const imageModal = new bootstrap.Modal(document.getElementById('imageModal'));
            imageModal.show();
        }
    }

     // Determine Assistant Avatar URL
     const assistantAvatarUrl = "{% if assistant.avatar %}{{ assistant.avatar.url|escapejs }}{% elif default_logo_url %}{{ default_logo_url|escapejs }}{% elif assistant.get_logo_url %}{{ assistant.get_logo_url|escapejs }}{% else %}null{% endif %}"; // Use null if no image found, escape URLs

     // Determine User Avatar URL
     const userAvatarUrl = "{% if request.user.is_authenticated and request.user.profile.avatar %}{{ request.user.profile.avatar.url|escapejs }}{% else %}null{% endif %}";


     // Helper function to get CSRF token
     function getCsrfToken() {
         const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
         if (!csrfInput) {
             const csrfMeta = document.querySelector('meta[name="csrf-token"]');
             if (csrfMeta) return csrfMeta.getAttribute('content');
         }
         return csrfInput ? csrfInput.value : null;
     }

     function getChatHistory() {
         const storedHistory = sessionStorage.getItem(sessionStorageKey);
         try {
             return storedHistory ? JSON.parse(storedHistory) : [];
         } catch (e) {
             console.error("Error parsing chat history:", e);
             return [];
         }
     }

     function saveChatHistory(history) {
         try {
             sessionStorage.setItem(sessionStorageKey, JSON.stringify(history));
         } catch (e) {
             console.error("Error saving chat history:", e);
         }
     }

      // Enhanced addMessage function with improved chat bubble orientation
      function addMessage(content, role, isHtml = false, images = [], gallery = [], interactionData = null) {
          const chatBox = document.getElementById('chat-box');
          if (!chatBox) return null; // Return null if chat box not found

        // Only hide initial display area when a user message is sent
        // This allows navigation content to be displayed alongside the welcome message
        const initialDisplayArea = chatBox.querySelector('#initial-display-area');
        if (role === 'user' && initialDisplayArea && initialDisplayArea.style.display !== 'none') {
            initialDisplayArea.style.display = 'none';
        }

        // Create the message container div with proper alignment classes
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', role === 'user' ? 'user-message' : 'assistant-message', 'mb-3');

        // Create avatar container
        const avatarContainer = document.createElement('div');
        avatarContainer.classList.add('chat-avatar-container', role === 'user' ? 'ms-2' : 'me-2');

        let avatarElement;

        // For assistant, check if we have a valid URL
        if (role === 'assistant') {
            if (assistantAvatarUrl && assistantAvatarUrl !== 'null') {
                avatarElement = document.createElement('img');
                avatarElement.src = assistantAvatarUrl;
                avatarElement.classList.add('chat-avatar');
                avatarElement.alt = "Assistant";
                avatarElement.loading = "lazy";
                avatarElement.decoding = "async";
            } else {
                // Create a fallback icon for assistant
                avatarElement = document.createElement('div');
                avatarElement.classList.add('chat-avatar-icon');
                const iconElement = document.createElement('i');
                iconElement.classList.add('bi', 'bi-robot');
                avatarElement.appendChild(iconElement);
            }
        } else {
            // For user, check if we have a valid URL
            if (userAvatarUrl && userAvatarUrl !== 'null') {
                avatarElement = document.createElement('img');
                avatarElement.src = userAvatarUrl;
                avatarElement.classList.add('chat-avatar');
                avatarElement.alt = "User";
                avatarElement.loading = "lazy";
                avatarElement.decoding = "async";
            } else {
                // Create a fallback icon for user
                avatarElement = document.createElement('div');
                avatarElement.classList.add('chat-avatar-icon');
                const iconElement = document.createElement('i');
                iconElement.classList.add('bi', 'bi-person-circle');
                avatarElement.appendChild(iconElement);
            }
        }
        avatarContainer.appendChild(avatarElement);


        // Create message content span with proper styling
        const messageContentSpan = document.createElement('span');
        messageContentSpan.classList.add('message-content', 'tinymce-content'); // Add tinymce-content class for responsive styling

        // Set the content
        if (isHtml) {
            // Pre-process HTML content to fix table structure issues
            let processedContent = content;

            // First fix any table structure issues
            if (typeof window.fixTableHtml === 'function' && content.includes('<table')) {
                processedContent = window.fixTableHtml(processedContent);
            }

            // Then process tables for proper display
            if (typeof window.processHtmlTables === 'function' && processedContent.includes('<table')) {
                processedContent = window.processHtmlTables(processedContent);
            }

            // Set the processed content
            messageContentSpan.innerHTML = processedContent;

            // Add special handling for contact information tables
            const contactTables = messageContentSpan.querySelectorAll('table:not(.contact-info)');
            contactTables.forEach(table => {
                const tableText = table.textContent.toUpperCase();
                if (tableText.includes('CHAT') || tableText.includes('OFFICE') ||
                    tableText.includes('CONTACT') || tableText.includes('LOCATION') ||
                    tableText.includes('ADDRESS') || tableText.includes('MEDIA')) {
                    table.classList.add('contact-info');
                }
            });
        } else {
            // Basic Markdown-like link detection (makes URLs clickable)
            const linkedContent = content.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
            messageContentSpan.innerHTML = linkedContent; // Use innerHTML even for plain text to render links
        }

        // Display images below text content
        if (images && images.length > 0) {
            const imageContainer = document.createElement('div');
            imageContainer.classList.add('mt-2'); // Add margin top to separate from text
            images.forEach(imageUrl => {
                const img = document.createElement('img');
                img.src = imageUrl;
                img.classList.add('chat-image', 'img-thumbnail', 'mb-1'); // Add Bootstrap thumbnail class
                img.style.cursor = 'pointer';
                img.onclick = () => openImageModal(imageUrl); // Open single image modal
                imageContainer.appendChild(img);
            });
            messageContentSpan.appendChild(imageContainer); // Append images container to message span
        }

        // Add "View Gallery" button if gallery exists
        if (gallery && gallery.length > 0) {
            const galleryBtn = document.createElement('button');
            galleryBtn.textContent = 'View Gallery';
            galleryBtn.classList.add('btn', 'btn-sm', 'btn-outline-secondary', 'mt-2'); // Margin top
            galleryBtn.onclick = function() { showGalleryModal(gallery); };
            messageContentSpan.appendChild(galleryBtn); // Append button after text/images
        }

        // Apply specific styling based on role
        if (role === 'user') {
            // Add content first, then avatar for user messages
            messageDiv.appendChild(messageContentSpan);
            messageDiv.appendChild(avatarContainer);

            // Immediately adjust the message bubble width if the function exists
            if (typeof window.adjustMessageBubbleWidth === 'function') {
                setTimeout(window.adjustMessageBubbleWidth, 10);
            }
        } else {
            // Add avatar first, then content for assistant messages
            messageDiv.appendChild(avatarContainer);
            messageDiv.appendChild(messageContentSpan);

            // Add upvote and flag buttons for community assistants (only for assistant messages, not user messages)
            if (assistantType === 'community') {
                const btnContainer = document.createElement('div');
                btnContainer.classList.add('message-actions', 'mt-2');

                // Create upvote button
                const upvoteBtn = document.createElement('button');
                upvoteBtn.type = 'button';
                upvoteBtn.classList.add('btn', 'btn-sm', 'btn-outline-success', 'upvote-answer-btn');
                upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up me-1"></i> Helpful';

                // Set the interaction ID for the upvote button
                if (interactionData && interactionData.id) {
                    upvoteBtn.setAttribute('data-interaction-id', interactionData.id);

                    // Add the ID as a title attribute
                    upvoteBtn.setAttribute('title', `Helpful`);
                } else {
                    upvoteBtn.setAttribute('data-interaction-id', '');
                    upvoteBtn.disabled = true;
                    upvoteBtn.setAttribute('title', 'Upvoting not available for this message');
                }

                btnContainer.appendChild(upvoteBtn);

                // Downvote button removed

                // Create flag button
                const flagBtn = document.createElement('button');
                flagBtn.type = 'button';
                flagBtn.classList.add('btn', 'btn-sm', 'btn-outline-warning', 'flag-answer-btn', 'ms-2');
                flagBtn.innerHTML = '<i class="bi bi-flag me-1"></i> Flag Answer';

                // Set data attributes for the flag button
                flagBtn.setAttribute('data-question', lastQuestion || '');
                flagBtn.setAttribute('data-answer', content.replace(/<[^>]*>/g, ''));

                btnContainer.appendChild(flagBtn);
                messageContentSpan.appendChild(btnContainer);
            }
        }


        chatBox.appendChild(messageDiv);
        // Use requestAnimationFrame to scroll smoothly after the browser has painted the new message
        requestAnimationFrame(() => {
            // Scroll to the input field at the bottom
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.scrollIntoView({ behavior: 'smooth', block: 'end' });
            } else {
                // Fallback to scrolling to the bottom of the page
                window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
            }
        });

        // Return the message div so it can be modified by the caller
        return messageDiv;
    }

    // --- Navigation Content Sync JS ---
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize TinyMCE on page load if we're on the community assistant
        if (assistantType === 'community') {
            // Check if we're on the contributions tab
            const contributionsTab = document.getElementById('contributions-tab');
            const contributionsPane = document.getElementById('contributions-pane');

            // Add event listener for tab show event
            if (contributionsTab) {
                contributionsTab.addEventListener('shown.bs.tab', function() {
                    if (typeof initializeTinyMCE === 'function' &&
                        (!window.tinyMCEInitialized || !tinymce.get('id_text_content'))) {
                        setTimeout(initializeTinyMCE, 100);
                    }
                });
            }

            // If the contributions tab is active, initialize TinyMCE immediately
            // Otherwise, it will be initialized when the tab is clicked
            if (contributionsPane && contributionsPane.classList.contains('active')) {
                if (typeof initializeTinyMCE === 'function' &&
                    (!window.tinyMCEInitialized || !tinymce.get('id_text_content'))) {
                    initializeTinyMCE();
                }
            } else {
                // If contributions tab is not active, we'll initialize when the tab is clicked
                // But we'll also try once with a delay to handle any edge cases
                setTimeout(function() {
                    if (typeof initializeTinyMCE === 'function' &&
                        (!window.tinyMCEInitialized || !tinymce.get('id_text_content'))) {
                        initializeTinyMCE();
                    }
                }, 1000);
            }
        }

        // Function to initialize TinyMCE with all necessary fixes
        function initializeTinyMCE() {
            // Check if TinyMCE is already initialized for the contributions textarea
            if (window.tinyMCEInitialized && tinymce.get('id_text_content')) {
                console.log("TinyMCE already initialized for id_text_content, skipping initialization");
                return;
            }

            // Count existing TinyMCE editors
            const existingEditors = document.querySelectorAll('.tox-tinymce').length;
            console.log(`Found ${existingEditors} existing TinyMCE editors on the page`);

            // Remove any existing editor instance first
            const existingEditor = tinymce.get('id_text_content');
            if (existingEditor) {
                existingEditor.remove();
            }

            // Initialize TinyMCE with responsive configuration
            tinymce.init({
                selector: '#id_text_content',
                height: 400,
                width: '100%',
                menubar: 'file edit view insert format tools table help',
                plugins: 'lists link image table wordcount autoresize tabfocus',
                toolbar: 'bold italic | alignleft aligncenter alignright | bullist numlist | link image table tableprops | hr',
                toolbar_mode: 'sliding',
                statusbar: true,
                branding: false,
                promotion: false,
                mobile: {
                    theme: 'silver',
                    plugins: 'lists link image table autoresize tabfocus',
                    toolbar: 'bold italic | bullist numlist | link image table',
                    table_grid: false,
                    table_responsive_width: true,
                    table_appearance_options: false
                },
                image_class_list: [
                    {title: 'Responsive', value: 'img-fluid'},
                    {title: 'Left Aligned', value: 'img-fluid float-start me-3'},
                    {title: 'Right Aligned', value: 'img-fluid float-end ms-3'},
                    {title: 'Centered', value: 'img-fluid mx-auto d-block'}
                ],
                image_dimensions: true,
                image_caption: true,
                resize_img_proportional: true,
                object_resizing: 'img,table',
                resize: true,
                table_responsive_width: true,
                table_default_attributes: {
                    border: '1',
                    cellpadding: '5',
                    cellspacing: '0',
                    class: 'table table-bordered'
                },
                table_default_styles: {
                    width: '100%',
                    'border-collapse': 'collapse'
                },
                table_appearance_options: false,
                table_advtab: true,
                table_cell_advtab: true,
                table_row_advtab: true,
                table_grid: true,
                table_toolbar: 'tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol',
                table_class_list: [
                    {title: 'None', value: ''},
                    {title: 'Responsive Table', value: 'table-responsive'},
                    {title: 'Bordered Table', value: 'table-bordered'},
                    {title: 'Striped Table', value: 'table-striped'},
                    {title: 'Small Table', value: 'table-sm'},
                    {title: 'Hover Effect', value: 'table-hover'}
                ],
                content_style: `
                    body {
                        font-family: Helvetica, Arial, sans-serif;
                        font-size: 14px;
                        min-height: 300px;
                        max-width: 100%;
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                        padding: 10px;
                        box-sizing: border-box;
                    }
                    img {
                        max-width: 100%;
                        height: auto !important;
                        display: block;
                        margin: 0.5em auto;
                        border-radius: 4px;
                    }
                    p {
                        margin: 0 0 1em 0;
                        line-height: 1.5;
                    }
                    /* Table wrapper for horizontal scrolling on small screens */
                    .table-wrapper {
                        width: 100%;
                        overflow-x: auto;
                        margin: 0.75rem 0;
                        border-radius: 0.5rem;
                        position: relative;
                        -webkit-overflow-scrolling: touch;
                    }
                    /* Improved table styling */
                    table {
                        width: 100% !important;
                        max-width: 100% !important;
                        margin-bottom: 1rem;
                        border-collapse: collapse !important;
                        border-spacing: 0 !important;
                        overflow: hidden !important;
                        table-layout: auto !important; /* Changed from fixed to auto for better content fitting */
                    }
                    /* Ensure tables don't overflow their containers */
                    table td, table th {
                        padding: 0.75rem !important;
                        vertical-align: top !important;
                        border: 1px solid #dee2e6 !important;
                        word-break: normal !important;
                        overflow-wrap: break-word !important;
                        min-width: 50px !important;
                    }
                    /* Responsive media queries */
                    @media (max-width: 992px) {
                        body { font-size: 15px; }
                        table { font-size: 14px; }
                        table td, table th { padding: 0.6rem !important; }
                    }
                    @media (max-width: 768px) {
                        body { font-size: 16px; }
                        table { font-size: 14px; border: none !important; }
                        table td, table th {
                            padding: 0.5rem !important;
                            min-width: 80px !important;
                        }
                        /* Auto-wrap tables in a scrollable container on mobile */
                        table:not(.table-wrapper table) {
                            display: block;
                            width: 100%;
                            overflow-x: auto;
                            -webkit-overflow-scrolling: touch;
                        }
                    }
                    @media (max-width: 576px) {
                        body { font-size: 16px; }
                        table { font-size: 13px; }
                        table td, table th {
                            padding: 0.4rem !important;
                            min-width: 70px !important;
                        }
                    }
                `,
                setup: function (editor) {
                    editor.on('change', function () {
                        editor.save();
                        countWords();
                    });
                    editor.on('keyup', function() {
                        countWords();
                    });

                    // Auto-wrap tables in a responsive container
                    editor.on('SetContent', function (e) {
                        if (e.content && e.content.indexOf('<table') !== -1) {
                            var content = editor.getContent();
                            var updatedContent = content.replace(/<table(?![^>]*class="[^"]*table-responsive[^"]*")[^>]*>/g,
                                '<div class="table-wrapper"><table $1>');
                            updatedContent = updatedContent.replace(/<\/table>/g, '</table></div>');
                            if (content !== updatedContent) {
                                editor.setContent(updatedContent);
                            }
                        }
                    });

                    // Handle paste events to ensure images and tables are responsive
                    editor.on('PastePostProcess', function (e) {
                        // Make images responsive
                        var imgs = e.node.getElementsByTagName('img');
                        for (var i = 0; i < imgs.length; i++) {
                            if (!imgs[i].hasAttribute('class') || imgs[i].getAttribute('class').indexOf('img-fluid') === -1) {
                                imgs[i].setAttribute('class', (imgs[i].getAttribute('class') || '') + ' img-fluid');
                            }
                            imgs[i].setAttribute('style', 'max-width: 100%; height: auto;');
                        }

                        // Handle tables in pasted content
                        var tables = e.node.getElementsByTagName('table');
                        for (var j = 0; j < tables.length; j++) {
                            if (!tables[j].parentNode.classList || !tables[j].parentNode.classList.contains('table-wrapper')) {
                                var wrapper = e.node.ownerDocument.createElement('div');
                                wrapper.className = 'table-wrapper';
                                tables[j].parentNode.insertBefore(wrapper, tables[j]);
                                wrapper.appendChild(tables[j]);
                            }

                            if (!tables[j].hasAttribute('class') || tables[j].getAttribute('class').indexOf('table') === -1) {
                                tables[j].setAttribute('class', (tables[j].getAttribute('class') || '') + ' table table-bordered');
                            }
                        }
                    });

                    // Add word count limit on paste
                    editor.on('PastePreProcess', function(e) {
                        // Get current content and calculate word count with pasted content
                        const currentContent = editor.getContent();
                        const combinedContent = currentContent + e.content;
                        const textOnly = combinedContent.replace(/<[^>]*>/g, ' ');
                        const words = textOnly.split(/\s+/).filter(word => word.length > 0);

                        if (words.length > 500) {
                            // Alert user that paste would exceed limit
                            alert(`Pasting this content would exceed the 500 word limit. The content has been truncated.`);

                            // Truncate the pasted content to fit within limit
                            const currentWords = currentContent.replace(/<[^>]*>/g, ' ')
                                .split(/\s+/).filter(word => word.length > 0);
                            const remainingWords = 500 - currentWords.length;

                            if (remainingWords <= 0) {
                                e.content = ''; // No room for more content
                            } else {
                                // Extract only the allowed number of words
                                const pastedWords = e.content.replace(/<[^>]*>/g, ' ')
                                    .split(/\s+/).filter(word => word.length > 0);
                                const truncatedWords = pastedWords.slice(0, remainingWords);
                                e.content = truncatedWords.join(' ');
                            }
                        }
                    });
                },
                init_instance_callback: function(editor) {
                    // Set the global flag to indicate TinyMCE has been initialized
                    window.tinyMCEInitialized = true;

                    // Make editor visible immediately
                    editor.getContainer().style.visibility = 'visible';

                    // Force visibility after a short delay
                    setTimeout(function() {
                        const editorContainer = editor.getContainer();
                        if (editorContainer) {
                            editorContainer.style.visibility = 'visible';
                            editorContainer.style.display = 'block';
                            // Set width to 100% to fit container
                            editorContainer.style.width = '100%';
                            // Remove any max-width constraints
                            editorContainer.style.maxWidth = 'none';
                        }

                        // Also ensure the iframe is visible and has focus
                        const iframe = document.querySelector('.tox-edit-area__iframe');
                        if (iframe) {
                            iframe.style.visibility = 'visible';
                            // Focus the editor to make it active for typing
                            editor.focus();
                        }

                        // Force all TinyMCE elements to be visible
                        document.querySelectorAll('.tox-tinymce, .tox-toolbar__primary, .tox-toolbar-overlord, .tox-edit-area, .tox-edit-area__iframe').forEach(function(el) {
                            el.style.visibility = 'visible';
                            el.style.display = el.tagName === 'DIV' ? 'block' : 'flex';
                        });

                        // Hide the path element
                        const pathElement = document.querySelector('.tox-statusbar__path');
                        if (pathElement) {
                            pathElement.style.display = 'none';
                        }

                        // Ensure the edit area is prominent
                        const editArea = document.querySelector('.tox-edit-area');
                        if (editArea) {
                            editArea.style.minHeight = '300px';
                            editArea.style.border = '1px solid #ccc';
                        }

                        // Initial word count
                        countWords();
                    }, 200);
                }
            });

            // Add word counter to the form if it doesn't exist
            const form = document.querySelector('form#knowledge-form');
            if (form) {
                let wordCounter = form.querySelector('.word-counter');
                if (!wordCounter) {
                    const textContentField = form.querySelector('#id_text_content');
                    if (textContentField) {
                        const counterDiv = document.createElement('div');
                        counterDiv.className = 'word-counter mt-1 text-end';
                        counterDiv.innerHTML = `
                            <small class="text-muted" id="word-count">0</small>
                            <small class="text-muted"> / 500 words</small>
                        `;
                        textContentField.parentNode.appendChild(counterDiv);

                        // Add warning message about word limit
                        const warningDiv = document.createElement('div');
                        warningDiv.className = 'alert alert-info mt-3 mb-3';
                        warningDiv.innerHTML = `
                            <i class="bi bi-info-circle me-2"></i>
                            <small>Keep your contribution under 500 words. Shorter, focused knowledge is more effective.</small>
                        `;
                        counterDiv.after(warningDiv);
                    }
                }

                // Add form submit handler to prevent submission if over limit
                form.addEventListener('submit', function(e) {
                    if (typeof tinymce !== 'undefined' && tinymce.get('id_text_content')) {
                        const content = tinymce.get('id_text_content').getContent();
                        const textOnly = content.replace(/<[^>]*>/g, ' ');
                        const words = textOnly.split(/\s+/).filter(word => word.length > 0);
                        if (words.length > 500) {
                            e.preventDefault();
                            alert('Your content exceeds the 500 word limit. Please shorten your text to submit.');
                            return false;
                        }
                    }
                });
            }
        }

        // Function to count words in TinyMCE editor
        function countWords() {
            if (typeof tinymce !== 'undefined' && tinymce.get('id_text_content')) {
                const content = tinymce.get('id_text_content').getContent();
                // Strip HTML tags
                const textOnly = content.replace(/<[^>]*>/g, ' ');
                // Count words
                const words = textOnly.split(/\s+/).filter(word => word.length > 0);
                const wordCount = words.length;

                // Update counter
                const counter = document.getElementById('word-count');
                if (counter) {
                    counter.textContent = wordCount;

                    // Change color based on count
                    if (wordCount > 500) {
                        counter.classList.remove('text-muted', 'text-warning');
                        counter.classList.add('text-danger');

                        // Find the submit button and disable it
                        const form = counter.closest('form');
                        if (form) {
                            const submitButtons = form.querySelectorAll('button[type="submit"]');
                            submitButtons.forEach(button => {
                                button.disabled = true;
                                button.title = `Content exceeds 500 word limit`;
                            });

                            // Add warning message if not already present
                            if (!form.querySelector('.word-limit-warning')) {
                                const warningDiv = document.createElement('div');
                                warningDiv.className = 'alert alert-danger mt-2 word-limit-warning';
                                warningDiv.innerHTML = `<i class="bi bi-exclamation-triangle-fill me-2"></i>Content exceeds the 500 word limit. Please shorten your text to submit.`;
                                counter.parentNode.after(warningDiv);
                            }
                        }
                    } else if (wordCount > 400) {
                        counter.classList.remove('text-muted', 'text-danger');
                        counter.classList.add('text-warning');

                        // Enable submit button
                        const form = counter.closest('form');
                        if (form) {
                            const submitButtons = form.querySelectorAll('button[type="submit"]');
                            submitButtons.forEach(button => {
                                button.disabled = false;
                                button.title = '';
                            });

                            // Remove warning if present
                            const warning = form.querySelector('.word-limit-warning');
                            if (warning) warning.remove();

                            // Add caution message if not already present
                            if (!form.querySelector('.word-limit-caution')) {
                                const cautionDiv = document.createElement('div');
                                cautionDiv.className = 'alert alert-warning mt-2 word-limit-caution';
                                cautionDiv.innerHTML = `<i class="bi bi-exclamation-circle me-2"></i>Approaching 500 word limit.`;
                                counter.parentNode.after(cautionDiv);
                            }
                        }
                    } else {
                        counter.classList.remove('text-warning', 'text-danger');
                        counter.classList.add('text-muted');

                        // Enable submit button
                        const form = counter.closest('form');
                        if (form) {
                            const submitButtons = form.querySelectorAll('button[type="submit"]');
                            submitButtons.forEach(button => {
                                button.disabled = false;
                                button.title = '';
                            });

                            // Remove warning and caution if present
                            const warning = form.querySelector('.word-limit-warning');
                            if (warning) warning.remove();

                            const caution = form.querySelector('.word-limit-caution');
                            if (caution) caution.remove();
                        }
                    }
                }
            }
        }
        const navButtons = document.querySelectorAll('.nav-button[data-section-id]');
        const contentBlocks = document.querySelectorAll('.nav-section-content-block');
        const initialDisplayArea = document.getElementById('initial-display-area'); // Get initial display area

        // Track the last clicked navigation item to prevent duplicate bubbles
        let lastClickedNavItem = null;
        let lastClickTimestamp = 0;
        let isProcessingNavClick = false;

        function showContentById(sectionId) {
            // Prevent multiple clicks while processing
            if (isProcessingNavClick) {
                return;
            }

            // Set processing flag
            isProcessingNavClick = true;

            // Find the nav button for this section to get its label and item ID
            let navLabel = "";
            let itemId = null;
            navButtons.forEach(btn => {
                if (btn.getAttribute('data-section-id') === sectionId) {
                    navLabel = btn.textContent.trim();
                    itemId = btn.getAttribute('data-item-id');
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });

            // If we don't have an item ID, we can't proceed
            if (!itemId) {
                isProcessingNavClick = false;
                return;
            }

            // Check if this is the same item that was just clicked (within the last 1 second)
            const currentTime = Date.now();
            if (lastClickedNavItem === itemId && (currentTime - lastClickTimestamp) < 1000) {
                isProcessingNavClick = false;
                return;
            }

            // Update last clicked item and timestamp
            lastClickedNavItem = itemId;
            lastClickTimestamp = currentTime;

            // Get the chat box but don't remove existing navigation bubbles
            const chatBox = document.getElementById('chat-box');
            // We'll keep all previous navigation bubbles to maintain chat history

            // Show loading message
            const loadingId = `loading-${Date.now()}`;
            addMessage(`<div id="${loadingId}" class="d-flex align-items-center"><span class="spinner-border spinner-border-sm me-2 text-primary" role="status" aria-hidden="true"></span> <span style="color: #333333;">Loading ${navLabel} content...</span></div>`, 'assistant', true);

            // Hide the initial display area
            if (initialDisplayArea) {
                initialDisplayArea.style.display = 'none';
            }

            // Hide initial greeting
            const greetingElement = initialDisplayArea ? initialDisplayArea.querySelector('.initial-greeting') : null;
            if (greetingElement) {
                greetingElement.style.display = 'none';
            }

            // Simulate a short delay to make it feel like we're fetching content
            setTimeout(() => {
                // Remove the loading message
                const loadingMsg = document.getElementById(loadingId);
                if (loadingMsg) {
                    const msgContainer = loadingMsg.closest('.message.assistant-message');
                    if (msgContainer) {
                        msgContainer.remove();
                    }
                }

                // Determine content based on item ID
                let content = '';

                // Add a timestamp to show when this navigation item was accessed
                const timestamp = new Date().toLocaleTimeString();

                // Handle specific items directly
                if (itemId === '21') {
                    // Vice President - use hardcoded content
                    content = `<div><strong>${navLabel}</strong> <small class="text-muted">(${timestamp})</small></div><hr><p>this is for the vice <strong>president</strong></p>`;
                } else if (itemId === '20') {
                    // Guild President - use content from the DOM
                    const guildPresidentBlock = document.getElementById('nav-section-guild-president');
                    if (guildPresidentBlock && guildPresidentBlock.innerHTML.trim() !== '') {
                        content = `<div><strong>${navLabel}</strong> <small class="text-muted">(${timestamp})</small></div><hr>${guildPresidentBlock.innerHTML}`;
                    } else {
                        content = `<div><strong>${navLabel}</strong> <small class="text-muted">(${timestamp})</small></div><hr><p>Guild President content not found.</p>`;
                    }
                } else {
                    // For other items, try to find the content block
                    const contentBlock = document.getElementById(`nav-section-${sectionId}`);
                    if (contentBlock && contentBlock.innerHTML.trim() !== '') {
                        content = `<div><strong>${navLabel}</strong> <small class="text-muted">(${timestamp})</small></div><hr>${contentBlock.innerHTML}`;
                    } else {
                        content = `<div><strong>${navLabel}</strong> <small class="text-muted">(${timestamp})</small></div><hr><p>No content available for this section.</p>`;
                    }
                }

                // Add the content to the chat with a special class to identify it as a nav bubble
                const messageBubble = addMessage(content, 'assistant', true);
                if (messageBubble) {
                    messageBubble.classList.add('nav-content-bubble');
                    // Add a visual indicator that this is navigation content
                    const contentSpan = messageBubble.querySelector('.message-content');
                    if (contentSpan) {
                        // Apply solid white background with blue left border
                        contentSpan.style.backgroundColor = '#ffffff';
                        contentSpan.style.background = '#ffffff';
                        contentSpan.style.backgroundImage = 'none';
                        contentSpan.style.color = '#333333';
                        contentSpan.style.border = '1px solid rgba(0, 0, 0, 0.05)';
                        contentSpan.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.03)';
                        contentSpan.style.borderLeft = '3px solid #0d6efd';
                    }

                    // Ensure we scroll to the input field
                    setTimeout(() => {
                        const messageInput = document.getElementById('message-input');
                        if (messageInput) {
                            messageInput.scrollIntoView({ behavior: 'smooth', block: 'end' });
                        } else {
                            // Fallback to scrolling to the bottom of the page
                            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                        }
                    }, 100);
                }

                // Reset processing flag
                isProcessingNavClick = false;

            }, 500); // Simulate a short delay
        }



        // Attach click listeners
        navButtons.forEach(btn => {
            // Remove any existing click listeners first to prevent duplicates
            const newBtn = btn.cloneNode(true);
            btn.parentNode.replaceChild(newBtn, btn);

            newBtn.addEventListener('click', function () {
                const sectionId = this.getAttribute('data-section-id');
                const itemId = this.getAttribute('data-item-id');
                showContentById(sectionId);
            });
        });

        // We'll no longer show the first nav item's content by default
        // Instead, we'll just make sure the greeting is visible
        const initialGreeting = initialDisplayArea ? initialDisplayArea.querySelector('.initial-greeting') : null;
        if(initialGreeting) initialGreeting.style.display = '';
    });
    // --- End Navigation Content Sync JS ---

    // Function to handle navigation button clicks
    function handleNavClicks() {
        document.querySelectorAll('.nav-button').forEach(button => {
            button.addEventListener('click', function() {
                const sectionId = this.getAttribute('data-section-id'); // This is the unique_id
                const sectionLabel = this.textContent.trim();
                const chatBox = document.getElementById('chat-box');

                // Close sidebar on mobile *before* sending message/simulating response
                const bodyElement = document.body;
                if (typeof isMobileView === 'function' && isMobileView() && !bodyElement.classList.contains('sidebar-collapsed')) {
                    if (typeof handleToggle === 'function') {
                        handleToggle(); // Use the existing toggle function
                    }
                }

                // Hide initial display if visible
                const initialDisplayArea = chatBox ? chatBox.querySelector('#initial-display-area') : null;
                if (initialDisplayArea && initialDisplayArea.style.display !== 'none') {
                    initialDisplayArea.style.display = 'none';
                }

                const thinkingMsgDiv = document.createElement('div');
                thinkingMsgDiv.classList.add('message', 'assistant-message', 'thinking');
                thinkingMsgDiv.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
                if (chatBox) chatBox.appendChild(thinkingMsgDiv);
                if (chatBox) chatBox.scrollTop = chatBox.scrollHeight;

                // Send specific payload for navigation click
                const interactUrl = `/assistant/company/{{ assistant.company.id }}/assistants/{{ assistant.id }}/interact/`;
                const csrfToken = getCsrfToken();
                const requestData = {
                    navigation_click: true,
                    section_id: sectionId
                };

                // Debug log for troubleshooting
                console.log('Navigation click - sending request to:', interactUrl);
                console.log('Navigation click - request data:', requestData);

                // AJAX request to backend
                fetch(interactUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify(requestData)
                })
                .then(response => {
                    console.log('Navigation click - response status:', response.status);
                    if (!response.ok) {
                        console.error('Navigation click - HTTP error:', response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Navigation click - response data:', data);
                    const thinkingDiv = chatBox.querySelector('.message.thinking');
                    if (thinkingDiv) chatBox.removeChild(thinkingDiv);
                    return data;
                })
                .then(data => {
                    // Expecting {status: 'success', content: 'html_string', is_navigation_response: true}
                    if (data.status === 'success' && data.content && data.is_navigation_response) {
                        // Display the formatted HTML content directly
                        addMessage(data.content, 'assistant', true); // isHtml = true
                    } else {
                        throw new Error(data.error || 'Received invalid response format for navigation click.');
                    }
                })
                .catch(error => {
                    console.error('Error handling nav click:', error);
                    // Provide more detailed error message for debugging
                    const errorDetails = error.message || 'Unknown error';
                    console.error('Navigation click - detailed error:', {
                        message: errorDetails,
                        url: interactUrl,
                        requestData: requestData
                    });
                    addMessage(`Error loading section: ${errorDetails}. Please check the console for more details.`, 'assistant', false);
                });
            });
        });
    }

    // Initialize navigation click handlers when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        handleNavClicks();

        // We don't need to load navigation-handler.js script here
        // since it's already loaded in the head section
    });

     // Suggestion functions - COMMENTED OUT
     /*
     function handleSuggestionClick(suggestionText) {
         const messageInput = document.getElementById('message-input');
         const sendButton = document.getElementById('send-button');
         if (!messageInput || !sendButton) return;
         messageInput.value = suggestionText;
         sendButton.click(); // Trigger the send message handler
     }

     function displaySuggestions(suggestions) {
         const suggestionsArea = document.getElementById('suggestions-area');
         if (!suggestionsArea) return;
         suggestionsArea.innerHTML = ''; // Clear previous suggestions
         if (suggestions && suggestions.length > 0) {
             suggestions.forEach(suggestion => {
                 const button = document.createElement('button');
                 button.classList.add('btn', 'btn-outline-secondary', 'btn-sm', 'me-1', 'mb-1', 'suggestion-btn'); // Added suggestion-btn class
                 button.textContent = suggestion;
                 button.onclick = () => handleSuggestionClick(suggestion); // Use the handler
                 suggestionsArea.appendChild(button);
             });
         }
     }
     */

     // Empty placeholder functions to avoid errors
     function handleSuggestionClick(suggestionText) {
         // Suggestions functionality is disabled
         console.log("Suggestions functionality is disabled");
     }

     function displaySuggestions(suggestions) {
         // Suggestions functionality is disabled
         console.log("Suggestions functionality is disabled");
     }

     // Function to fetch initial suggestions when the page loads - COMMENTED OUT
     /*
     function fetchInitialSuggestions() {
         const suggestionsArea = document.getElementById('suggestions-area');
         if (!suggestionsArea) return;

         // Show loading state
         suggestionsArea.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm text-secondary" role="status"><span class="visually-hidden">Loading...</span></div> <small class="text-muted">Loading suggestions...</small></div>';

         // Try to use initial_suggestions from template context first
         try {
             const initialSuggestions = JSON.parse('{{ initial_suggestions|default:"[]"|escapejs }}');
             if (initialSuggestions && initialSuggestions.length > 0) {
                 displaySuggestions(initialSuggestions);
                 return; // Exit if we have initial suggestions
             }
         } catch (e) {
             console.error('Error parsing initial suggestions:', e);
         }

         // If no initial suggestions, make a special API call to get them
         fetch(`/assistants/company/${companyId}/assistants/${assistantId}/interact/`, {
             method: 'POST',
             headers: {
                 'Content-Type': 'application/json',
                 'X-CSRFToken': getCsrfToken()
             },
             body: JSON.stringify({
                 message: "Hello, I'm new here. What can you help me with?",
                 history: []
             })
         })
         .then(response => {
             if (!response.ok) {
                 throw new Error(`HTTP error! status: ${response.status}`);
             }
             return response.json();
         })
         .then(data => {
             if (data.suggestions && data.suggestions.length > 0) {
                 displaySuggestions(data.suggestions);
             } else {
                 suggestionsArea.innerHTML = ''; // Clear loading state if no suggestions
             }
         })
         .catch(error => {
             console.error('Error fetching initial suggestions:', error);
             suggestionsArea.innerHTML = ''; // Clear loading state on error
         });
     }
     */
     // Empty placeholder function to avoid errors
     function fetchInitialSuggestions() {
         // Suggestions functionality is disabled
         console.log("Suggestions functionality is disabled");
     }

     function handleSendMessage() {
         const messageInput = document.getElementById('message-input');
         const chatBox = document.getElementById('chat-box');
         // Suggestions functionality removed
         const sendButton = document.getElementById('send-button');



         if (!messageInput || !chatBox || !sendButton) return;

        // Hide initial display area on send if it's still visible
        const initialDisplayArea = chatBox.querySelector('#initial-display-area');
        if (initialDisplayArea && initialDisplayArea.style.display !== 'none') {
            // Store the initial display HTML if not already stored
            if (!initialDisplayAreaHTML) {
                initialDisplayAreaHTML = initialDisplayArea.outerHTML;
            }
            // Hide the initial display area when user sends a message
            // Use the global function if available for consistent hiding
            if (typeof window.hideWelcomeMessage === 'function') {
                window.hideWelcomeMessage();
            } else {
                // Fallback to direct style manipulation
                initialDisplayArea.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; height: 0 !important; overflow: hidden !important; margin: 0 !important; padding: 0 !important;';
            }
            console.log('Initial display area hidden by handleSendMessage');
        }

        const message = messageInput.value.trim();
        if (!message) return;

        // Store the last question for flagging - this is important for the flag button
        lastQuestion = message;

        const historyForBackend = getChatHistory(); // Get history *before* adding new message
        addMessage(message, 'user', false); // User message is plain text
        messageInput.value = ''; // Clear input after sending
        messageInput.focus(); // Keep focus on input
        sendButton.disabled = true; // Disable send button while waiting

        // Suggestions functionality removed

        const interactUrl = `/assistant/company/${companyId}/assistants/${assistantId}/interact/`;
        const csrfToken = getCsrfToken();

        // Add enhanced thinking indicator using addMessage for consistent styling
        const thinkingId = `thinking-${Date.now()}`; // Unique ID for the thinking message
        addMessage(`
            <div id="${thinkingId}" class="thinking-indicator">
                <span class="thinking-text">Thinking</span>
                <div class="thinking-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `, 'assistant', true, [], [], null);
        // Scroll to the input field (using the messageInput variable already defined above)
        messageInput.scrollIntoView({ behavior: 'smooth', block: 'end' });

        // Prepare data as JSON, including history
        const requestData = {
            message: message,
            history: historyForBackend, // Include the retrieved history
            use_community_context: assistantType === 'community' ? true : false // Always use community context for community assistants
        };


        fetch(interactUrl, {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json' // Set content type to JSON
            },
            body: JSON.stringify(requestData) // Send data as JSON string
        })
        .then(response => response)
        .then(response => {
             // Remove thinking indicator regardless of success/failure
             const thinkingDiv = chatBox.querySelector(`#${thinkingId}`)?.closest('.message.assistant-message');
             if (thinkingDiv) {
                 chatBox.removeChild(thinkingDiv);
             }

             if (!response.ok) {
                 // Try to get more specific error from JSON body if possible
                 return response.json().then(errData => {
                     throw new Error(errData.error || errData.content || `HTTP error ${response.status}`);
                 }).catch(() => {
                     // Fallback if response is not JSON or parsing fails
                     throw new Error(`HTTP error ${response.status}`);
                 });
             }
             return response.json(); // If response is ok, parse JSON
        })
        .then(data => {
            // Process the successful response data
            if (data.content) {
                const isHtmlResponse = data.is_html || false;
                const images = data.images || [];
                const gallery = data.gallery || [];
                const usedContexts = data.used_contexts || [];

                // Add the message content with interaction data
                // Create interaction data object with the correct ID
                const interactionData = {
                    id: data.interaction_id || null  // Use interaction_id as the primary ID
                };
                addMessage(data.content, 'assistant', isHtmlResponse, images, gallery, interactionData);

                // If there are used contexts, add links to them
                if (usedContexts && usedContexts.length > 0) {
                    // Find the last assistant message
                    const chatBox = document.getElementById('chat-box');
                    const lastAssistantMessage = chatBox.querySelector('.message.assistant-message:last-child');
                    const btnContainer = lastAssistantMessage.querySelector('.message-actions');

                    if (lastAssistantMessage) {
                        // Create a container for the context links
                        const contextLinksContainer = document.createElement('div');
                        contextLinksContainer.classList.add('context-links', 'mt-3', 'small', 'bg-light', 'p-2', 'rounded', 'border', 'context-links-container');

                        // Add a heading for the sources
                        const sourcesHeading = document.createElement('div');
                        sourcesHeading.classList.add('fw-bold', 'mb-2', 'context-links-header');
                        sourcesHeading.innerHTML = '<i class="bi bi-book me-1"></i> Sources Used:';
                        contextLinksContainer.appendChild(sourcesHeading);

                        // Create a list for the context links
                        const contextList = document.createElement('div');
                        contextList.classList.add('d-flex', 'flex-wrap', 'gap-2', 'context-links-container');

                        // Add links for each context
                        usedContexts.forEach((context, index) => {
                            const contextLink = document.createElement('a');
                            contextLink.href = '#';
                            contextLink.classList.add('context-link', 'badge', 'bg-primary', 'text-decoration-none');
                            contextLink.innerHTML = `<i class="bi bi-file-text me-1"></i>${context.title}`;
                            contextLink.dataset.contextId = context.id;
                            contextLink.dataset.contextTitle = context.title;
                            contextLink.dataset.contextCreatedBy = context.created_by;
                            contextLink.dataset.contextCreatedAt = context.created_at;

                            // Add click event to show context modal
                            contextLink.addEventListener('click', function(e) {
                                e.preventDefault();
                                showContextModal(context.id, context.title, context.created_by, context.created_at);
                            });

                            contextList.appendChild(contextLink);
                        });

                        // Add the list to the container
                        contextLinksContainer.appendChild(contextList);

                        // Add a note about community contributions
                        const contributionNote = document.createElement('div');
                        contributionNote.classList.add('mt-2', 'small', 'text-muted');
                        contributionNote.innerHTML = '<i class="bi bi-people me-1"></i> Community knowledge helped answer this question!';
                        contextLinksContainer.appendChild(contributionNote);

                        // Append the context links container after the button container
                        if (btnContainer) {
                            btnContainer.after(contextLinksContainer);
                        } else {
                            // Fallback if button container not found
                            lastAssistantMessage.appendChild(contextLinksContainer);
                        }
                    }
                }

                // Update history after successful response
                let currentHistory = getChatHistory();
                // Limit history size (e.g., last 20 messages) to prevent excessive storage/payload
                const maxHistoryLength = 20;
                currentHistory.push({ role: 'user', content: message });
                currentHistory.push({
                    role: 'assistant',
                    content: data.content,
                    is_html: isHtmlResponse,
                    images: images,
                    gallery: gallery,
                    interaction_data: {
                        id: data.interaction_id || null  // Use interaction_id as the primary ID
                    }
                }); // Store with metadata
                if (currentHistory.length > maxHistoryLength) {
                    currentHistory = currentHistory.slice(currentHistory.length - maxHistoryLength);
                }
                saveChatHistory(currentHistory);

                // Suggestions functionality removed
            } else if (data.error) {
                // Handle application-level errors returned in JSON
                addMessage(`Error: ${data.error}`, 'assistant', false); // Display error as plain text
                console.error("Assistant Error:", data.error);
                // Suggestions functionality removed
            }
        })
        .catch(error => {
            // Handle network errors or errors thrown from .then blocks
            console.error('Send Message Fetch/Processing Error:', error);
            addMessage(`Sorry, I encountered an error: ${error.message}`, 'assistant', false);
             // Ensure thinking indicator is removed if fetch itself failed
             const thinkingDiv = chatBox.querySelector(`#${thinkingId}`)?.closest('.message.assistant-message');
             if (thinkingDiv) {
                 chatBox.removeChild(thinkingDiv);
             }
             // Suggestions functionality removed
        })
        .finally(() => {
             sendButton.disabled = false; // Re-enable send button
             messageInput.focus(); // Ensure focus remains on input
        }); // Close the fetch promise chain
    } // Close handleSendMessage function

    // --- Event Listeners ---
    document.addEventListener('DOMContentLoaded', () => {
        // Store initial display area HTML ONCE on load
        const initialArea = document.getElementById('initial-display-area');
        if (initialArea) {
            // Store the whole initial area including avatar, nav content placeholders, and greeting
            initialDisplayAreaHTML = initialArea.outerHTML;

            // Make sure the initial area is visible
            initialArea.style.display = 'block';

            // Make sure the initial greeting is visible
            const initialGreeting = initialArea.querySelector('.initial-greeting');
            if (initialGreeting) {
                initialGreeting.style.display = 'block';
            }
        }

        // Suggestions functionality removed

        // Load chat history on page load
        const chatHistory = getChatHistory();
        const chatBox = document.getElementById('chat-box');
        if (chatHistory.length > 0 && chatBox) {
            // If history exists, hide the initial display area immediately
            if (initialArea) {
                initialArea.style.display = 'none';
            }
            chatHistory.forEach(msg => {
                // Determine if message content is HTML - NEED TO STORE THIS FLAG IN HISTORY
                // For now, assume false for simplicity, but this needs fixing
                 const isHtml = msg.is_html || false; // Assuming history stores this flag
                 const images = msg.images || [];
                 const gallery = msg.gallery || [];
                 const interactionData = msg.interaction_data || null;
                addMessage(msg.content, msg.role, isHtml, images, gallery, interactionData);
            });
             // Scroll to the input field after loading history
             requestAnimationFrame(() => {
                 const messageInput = document.getElementById('message-input');
                 if (messageInput) {
                     messageInput.scrollIntoView({ behavior: 'smooth', block: 'end' });
                 } else {
                     // Fallback to scrolling to the bottom of the page
                     window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                 }
             });
        } else if (initialArea) {
             // Ensure initial area is visible if no history
             initialArea.style.display = '';
        }


        const sendButton = document.getElementById('send-button');
        const messageInput = document.getElementById('message-input');
        const chatForm = document.getElementById('chat-form');
        const resetBtn = document.getElementById('reset-chat-btn');

        if (sendButton && messageInput) {
            // Make sure the send button is enabled
            sendButton.disabled = false;

            // Add click event listener to send button
            sendButton.addEventListener('click', function(e) {
                e.preventDefault(); // Prevent any default action
                handleSendMessage();
                return false; // Prevent form submission
            });

            // Add keypress event listener to message input
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault(); // Prevent default form submission/newline
                    handleSendMessage();
                    return false; // Prevent form submission
                }
            });

            // Enable/disable send button based on input content
            // Initial state - check if there's already text in the input
            sendButton.disabled = messageInput.value.trim() === '';

            // Add input event listener to update button state whenever text changes
            messageInput.addEventListener('input', function() {
                // Enable button if there's text, disable if empty
                sendButton.disabled = this.value.trim() === '';
            });

            // Also check on focus to ensure button state is correct
            messageInput.addEventListener('focus', function() {
                sendButton.disabled = this.value.trim() === '';
            });

            // Focus on the input field
            messageInput.focus();
        }
        // Prevent form submission for the chat form itself
        if (chatForm) {
            // Remove any existing event listeners
            const newChatForm = chatForm.cloneNode(true);
            chatForm.parentNode.replaceChild(newChatForm, chatForm);

            // Add the event listener to the new form
            newChatForm.addEventListener('submit', (e) => {
                e.preventDefault();
                e.stopPropagation();
                handleSendMessage(); // Also trigger send on form submit
                return false;
            });

            // Update references to form elements
            const newSendButton = newChatForm.querySelector('#send-button');
            const newMessageInput = newChatForm.querySelector('#message-input');

            // Add event listeners to the new elements
            if (newSendButton) {
                newSendButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleSendMessage();
                    return false;
                });
            }

            if (newMessageInput) {
                newMessageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                        return false;
                    }
                });

                // Add input event listener to update button state whenever text changes
                newMessageInput.addEventListener('input', function() {
                    if (newSendButton) {
                        // Enable button if there's text, disable if empty
                        newSendButton.disabled = this.value.trim() === '';
                    }
                });

                // Also check on focus to ensure button state is correct
                newMessageInput.addEventListener('focus', function() {
                    if (newSendButton) {
                        newSendButton.disabled = this.value.trim() === '';
                    }
                });

                // Focus on the input field
                newMessageInput.focus();

                // Set initial button state based on current input value
                if (newSendButton) {
                    newSendButton.disabled = newMessageInput.value.trim() === '';
                }
            }
        }

        // Reset button functionality
        if (resetBtn && chatBox) {
            resetBtn.addEventListener('click', () => {
                // Directly reset without confirmation dialog
                sessionStorage.removeItem(sessionStorageKey); // Clear history

                // Clear all messages from the chat box except the initial greeting
                const messages = chatBox.querySelectorAll('.message:not(.initial-greeting)');
                messages.forEach(message => message.remove());

                // Show the welcome message if it exists
                const initialDisplayArea = document.getElementById('initial-display-area');
                if (initialDisplayArea) {
                    // Use the global function if available for consistent showing
                    if (typeof window.showWelcomeMessage === 'function') {
                        window.showWelcomeMessage();
                    } else {
                        // Fallback to direct style manipulation
                        initialDisplayArea.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important; height: auto !important; overflow: visible !important; margin-bottom: 1rem !important; padding: 0 !important;';

                        // Also make sure all child elements are visible
                        const children = initialDisplayArea.querySelectorAll('*');
                        children.forEach(child => {
                            if (child.classList.contains('assistant-profile-pic') ||
                                child.classList.contains('assistant-profile-pic-placeholder') ||
                                child.classList.contains('initial-greeting') ||
                                child.classList.contains('welcome-text-small')) {
                                child.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important;';
                            }
                        });
                    }
                    console.log('Initial display area shown by reset button click');
                }

                // Reset the last clicked item tracking
                lastClickedNavItem = null;
                lastClickTimestamp = 0;

                // Clear any stored navigation content
                currentNavContext = '';

                // Re-run the nav setup logic for the restored initial state
                setupNavigation();

                // Ensure input is enabled and focused
                if(messageInput) messageInput.value = '';
                if(sendButton) sendButton.disabled = true;
                if(messageInput) messageInput.focus();
            });
        }

        // Function to encapsulate nav setup (to call on load and reset)
        function setupNavigation() {
            const navButtons = document.querySelectorAll('.nav-button[data-section-id]');
            const contentBlocks = document.querySelectorAll('.nav-section-content-block');
            const initialDisplayArea = document.getElementById('initial-display-area');

            // Hide all navigation content blocks by default
            contentBlocks.forEach(block => {
                block.style.display = 'none';
            });

            // Make sure the nav section content container is hidden
            const navSectionContent = document.getElementById('nav-section-content');
            if (navSectionContent) {
                navSectionContent.style.display = 'none';
            }

            // Just make sure the greeting is visible
            const initialGreeting = initialDisplayArea ? initialDisplayArea.querySelector('.initial-greeting') : null;
            if(initialGreeting) initialGreeting.style.display = 'block';

            // We don't need to add click handlers here as they're already handled in the navigation content sync JS section
            // This prevents duplicate event handlers that cause double posts
        }

        // Initial setup
        setupNavigation(); // Call the nav setup

        // Sidebar Toggle Logic is now handled by sidebar-toggle.js
        // No duplicate code needed here

        // Copy URL Button
        const copyUrlBtn = document.getElementById('copy-url-btn');
        if (copyUrlBtn) {
            copyUrlBtn.addEventListener('click', () => {
                navigator.clipboard.writeText(window.location.href).then(() => {
                    const originalText = copyUrlBtn.textContent;
                    copyUrlBtn.textContent = 'Copied!';
                    copyUrlBtn.disabled = true;
                    setTimeout(() => {
                        copyUrlBtn.textContent = originalText;
                        copyUrlBtn.disabled = false;
                    }, 1500);
                }).catch(err => {
                    console.error('Failed to copy URL: ', err);
                    alert('Failed to copy URL. Please copy it manually.');
                });
            });
        }

        // QR Code Modal Logic
        const qrCodeModal = document.getElementById('qrCodeModal');
        if (qrCodeModal) {
            qrCodeModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget; // Button that triggered the modal
                const qrUrl = button.getAttribute('data-qr-url');
                const isCompany = button.getAttribute('data-is-company') === 'true';
                const companyName = button.getAttribute('data-company-name');
                const modalImage = qrCodeModal.querySelector('#qrCodeImage');
                const downloadLink = qrCodeModal.querySelector('#qrCodeDownloadLink');
                const description = qrCodeModal.querySelector('#qrCodeDescription');
                const modalTitle = qrCodeModal.querySelector('.modal-title');

                // Handle case when QR code doesn't exist yet
                if (!qrUrl) {
                    // Don't show the modal if there's no QR code
                    const modalInstance = bootstrap.Modal.getInstance(qrCodeModal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                    return;
                }

                // QR code exists, show it with improved styling
                if (modalImage) {
                    modalImage.style.display = 'block';
                    modalImage.src = qrUrl;

                    // Ensure the container is visible
                    const qrContainer = modalImage.closest('.qr-code-container');
                    if (qrContainer) {
                        qrContainer.style.display = 'flex';
                    }
                }
                if (downloadLink) {
                    downloadLink.style.display = 'inline-block';
                    downloadLink.href = qrUrl; // Set download link href
                    // Update download filename based on type
                    if (isCompany) {
                        downloadLink.setAttribute('download', `${companyName.replace(/\s+/g, '_')}_qr_code.png`);
                    } else {
                        downloadLink.setAttribute('download', 'assistant_qr_code.png');
                    }
                }

                // Update description and title based on QR code type
                if (isCompany && description && companyName) {
                    description.textContent = `Scan this code to access ${companyName}.`;
                    if (modalTitle) modalTitle.textContent = `${companyName} QR Code`;
                } else {
                    description.textContent = 'Scan this code to open the chat.';
                    if (modalTitle) modalTitle.textContent = 'Scan QR Code';
                }
            });
        }

        // Rating Modal Logic
        const ratingModal = document.getElementById('ratingModal');
        const submitRatingBtn = document.getElementById('submitRatingBtn');
        const modalStarsContainer = ratingModal ? ratingModal.querySelector('.modal-stars') : null;
        const modalErrorMsg = document.getElementById('modalErrorMsg');
        let selectedRating = 0;
        let ratingAssistantId = null; // To store which assistant is being rated

        if (ratingModal) {
            ratingModal.addEventListener('show.bs.modal', function (event) {
                const button = event.relatedTarget;
                ratingAssistantId = button.getAttribute('data-assistant-id');
                const assistantName = button.getAttribute('data-assistant-name');
                const modalTitle = ratingModal.querySelector('#ratingModalLabel');
                const modalAssistantName = ratingModal.querySelector('#modalAssistantName');

                if (modalTitle) modalTitle.textContent = `Rate ${assistantName}`;
                if (modalAssistantName) modalAssistantName.textContent = assistantName;

                selectedRating = 0;
                if (submitRatingBtn) submitRatingBtn.disabled = true;
                if (modalErrorMsg) modalErrorMsg.style.display = 'none';
                if (modalStarsContainer) {
                    modalStarsContainer.querySelectorAll('.modal-star-btn i').forEach(starIcon => {
                        starIcon.classList.remove('bi-star-fill', 'text-warning');
                        starIcon.classList.add('bi-star', 'text-secondary');
                    });
                }
            });
        }

        if (modalStarsContainer) {
            modalStarsContainer.addEventListener('click', function (event) {
                const starButton = event.target.closest('.modal-star-btn');
                if (!starButton) return;

                selectedRating = parseInt(starButton.getAttribute('data-rating-value'));
                if (submitRatingBtn) submitRatingBtn.disabled = false;
                if (modalErrorMsg) modalErrorMsg.style.display = 'none';

                modalStarsContainer.querySelectorAll('.modal-star-btn').forEach(btn => {
                    const ratingValue = parseInt(btn.getAttribute('data-rating-value'));
                    const starIcon = btn.querySelector('i');
                    if (ratingValue <= selectedRating) {
                        starIcon.classList.remove('bi-star', 'text-secondary');
                        starIcon.classList.add('bi-star-fill', 'text-warning');
                    } else {
                        starIcon.classList.remove('bi-star-fill', 'text-warning');
                        starIcon.classList.add('bi-star', 'text-secondary');
                    }
                });
            });
        }

        if (submitRatingBtn) {
            submitRatingBtn.addEventListener('click', function () {
                if (selectedRating > 0 && ratingAssistantId) {
                    const rateUrl = `/directory/rate-assistant/${ratingAssistantId}/`;
                    const csrfToken = getCsrfToken();
                    submitRatingBtn.disabled = true; // Disable while submitting

                    fetch(rateUrl, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ rating: selectedRating })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const ratingDisplay = document.getElementById(`rating-display-${ratingAssistantId}`);
                            if (ratingDisplay) {
                                ratingDisplay.innerHTML = data.rating_html;
                            }
                            const successMsgSpan = document.getElementById(`rating-msg-${ratingAssistantId}`);
                            if (successMsgSpan) {
                                successMsgSpan.textContent = 'Rating submitted!';
                                successMsgSpan.style.display = 'inline';
                                setTimeout(() => { successMsgSpan.style.display = 'none'; }, 3000);
                            }
                            // Clear any error messages before hiding the modal
                            if (modalErrorMsg) {
                                modalErrorMsg.style.display = 'none';
                                modalErrorMsg.textContent = '';
                            }

                            const modalInstance = bootstrap.Modal.getInstance(ratingModal);
                            if (modalInstance) modalInstance.hide();
                        } else {
                            if (modalErrorMsg) {
                                modalErrorMsg.textContent = data.error || 'Failed to submit rating.';
                                modalErrorMsg.style.display = 'block';
                            }
                             submitRatingBtn.disabled = false; // Re-enable on error
                        }
                    })
                    .catch(error => {
                        console.error('Rating submission error:', error);
                        if (modalErrorMsg) {
                            modalErrorMsg.textContent = 'An error occurred. Please try again.';
                            modalErrorMsg.style.display = 'block';
                        }
                         submitRatingBtn.disabled = false; // Re-enable on error
                    });
                }
            });
        }

        // Add event delegation for flag and upvote buttons (they're added dynamically)
        if (assistantType === 'community') {
            document.addEventListener('click', function(event) {
                // Flag button handling is done below

                // Handle upvote button clicks
                if (event.target.closest('.upvote-answer-btn')) {
                    const upvoteBtn = event.target.closest('.upvote-answer-btn');
                    const interactionId = upvoteBtn.getAttribute('data-interaction-id');
                    console.log('Upvote button clicked for interaction:', interactionId);

                    if (!interactionId) {
                        console.warn('No interaction ID found for upvote button');
                        return;
                    }

                    const csrfToken = getCsrfToken();
                    if (!csrfToken) {
                        console.error('No CSRF token found');
                        return;
                    }

                    // Construct the correct upvote URL
                    const upvoteUrl = `/assistant/interaction/${interactionId}/upvote/`;
                    console.log('Sending upvote request to:', upvoteUrl);

                    // Send AJAX request to upvote
                    fetch(upvoteUrl, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({}) // Send empty JSON object
                    })
                    .then(response => {
                        console.log('Upvote response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Upvote response data:', data);
                        if (data.status === 'success') {
                            // Update the button appearance
                            if (data.upvote_status === 'added') {
                                upvoteBtn.classList.remove('btn-outline-success');
                                upvoteBtn.classList.add('btn-success');
                                upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up-fill me-1"></i> Helpful';

                                // Downvote button reset removed
                            }

                            if (data.upvote_status === 'added') {
                                // Show a thank you message
                                const thankYouDiv = document.createElement('div');
                                thankYouDiv.className = 'small text-success mt-1';
                                thankYouDiv.innerHTML = '<i class="bi bi-check-circle me-1"></i> ' + (data.message || 'Thank you for your feedback!');
                                upvoteBtn.parentNode.appendChild(thankYouDiv);

                                // Remove the message after 3 seconds
                                setTimeout(() => {
                                    thankYouDiv.remove();
                                }, 3000);
                            } else {
                                upvoteBtn.classList.remove('btn-success');
                                upvoteBtn.classList.add('btn-outline-success');
                                upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up me-1"></i> Helpful';
                            }
                        } else {
                            console.error('Upvote failed:', data.message || 'Unknown error');
                            // Show error message
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'small text-danger mt-1';
                            errorDiv.innerHTML = '<i class="bi bi-exclamation-circle me-1"></i> ' + (data.message || 'Failed to upvote. Please try again.');
                            upvoteBtn.parentNode.appendChild(errorDiv);

                            // Remove the message after 3 seconds
                            setTimeout(() => {
                                errorDiv.remove();
                            }, 3000);
                        }
                    })
                    .catch(error => {
                        console.error('Error upvoting answer:', error);

                        // Show error message
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'small text-danger mt-1';
                        errorDiv.innerHTML = '<i class="bi bi-exclamation-circle me-1"></i> Error: ' + error.message;
                        upvoteBtn.parentNode.appendChild(errorDiv);

                        // Remove the message after 5 seconds
                        setTimeout(() => {
                            errorDiv.remove();
                        }, 5000);
                    });
                }

                // Downvote button handler removed


                // Continue with flag button handling
                if (event.target.closest('.flag-answer-btn')) {
                    const flagBtn = event.target.closest('.flag-answer-btn');
                    const question = flagBtn.getAttribute('data-question');
                    const answer = flagBtn.getAttribute('data-answer');

                    // Check if user is authenticated
                    const isAuthenticated = "{{ user.is_authenticated|yesno:'true,false' }}" === 'true';

                    if (isAuthenticated) {
                        // Redirect to the flag question page for logged-in users
                        window.location.href = `/assistants/company/${companyId}/assistants/${assistantId}/flag-question/?question=${encodeURIComponent(question)}&answer=${encodeURIComponent(answer)}`;
                    } else {
                        // Show modal for anonymous users
                        const flagModal = document.getElementById('flagAnswerModal');
                        if (flagModal) {
                            // Set the question and answer in the form and display areas
                            document.getElementById('flag-question').value = question;
                            document.getElementById('flag-answer').value = answer;

                            // Display the question and answer in the modal
                            const questionDisplay = document.getElementById('modal-question-display');
                            const answerDisplay = document.getElementById('modal-answer-display');

                            if (questionDisplay) questionDisplay.textContent = question;
                            if (answerDisplay) answerDisplay.textContent = answer;

                            // Show the modal
                            const modal = new bootstrap.Modal(flagModal);
                            modal.show();

                            // Handle submit button click
                            document.getElementById('submit-flag-btn').onclick = function() {
                                const form = document.getElementById('flag-answer-form');
                                const formData = new FormData(form);

                                // Add email from the modal form
                                const emailInput = document.getElementById('flag-email');
                                if (emailInput && emailInput.value) {
                                    formData.append('email', emailInput.value);
                                }

                                // Send AJAX request to flag the question
                                fetch(`/assistants/company/${companyId}/assistants/${assistantId}/flag-from-chat/`, {
                                    method: 'POST',
                                    headers: {
                                        'X-CSRFToken': getCsrfToken(),
                                        'X-Requested-With': 'XMLHttpRequest'
                                    },
                                    body: formData
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (data.status === 'success') {
                                        // Hide the modal
                                        modal.hide();

                                        // Show success message
                                        alert(data.message || 'Question flagged successfully!');

                                        // Redirect to the flagged question detail page
                                        window.location.href = data.url + '?tracking_id=' + data.tracking_id;
                                    } else {
                                        alert('Error: ' + (data.message || 'Failed to flag this question'));
                                    }
                                })
                                .catch(error => {
                                    console.error('Error flagging question:', error);
                                    alert('An error occurred while flagging this question. Please try again.');
                                });
                            };
                        }
                    }
                }
            });

            // Handle tab switching for community assistants
            const communityTabs = document.getElementById('communityTabs');
            if (communityTabs) {
                // Load content when switching to Contributions tab
                const contributionsTab = document.getElementById('contributions-tab');
                contributionsTab.addEventListener('shown.bs.tab', function (e) {
                    loadRecentContributions();

                    // Re-initialize TinyMCE when switching to Contributions tab
                    // Only if it hasn't been initialized yet
                    if (typeof tinymce !== 'undefined' && typeof initializeTinyMCE === 'function' &&
                        (!window.tinyMCEInitialized || !tinymce.get('id_text_content'))) {
                        // Use the initializeTinyMCE function we defined earlier
                        setTimeout(initializeTinyMCE, 100);
                    }
                });

                // Load content when switching to Assist tab
                const assistTab = document.getElementById('assist-tab');
                assistTab.addEventListener('shown.bs.tab', function (e) {
                    loadFlaggedQuestions();
                });
            }

            // Function to load recent contributions
            function loadRecentContributions() {
                const container = document.getElementById('recent-contexts-container');
                if (!container) return;

                fetch(`/assistants/company/${companyId}/assistants/${assistantId}/contexts/recent/`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.text();
                    })
                    .then(html => {
                        container.innerHTML = html;
                    })
                    .catch(error => {
                        container.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Error loading contributions: ${error.message}
                            </div>
                        `;
                    });
            }

            // Function to load flagged questions
            function loadFlaggedQuestions() {
                const container = document.getElementById('flagged-questions-container');
                if (!container) return;

                fetch(`/assistants/company/${companyId}/assistants/${assistantId}/flagged/recent/`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.text();
                    })
                    .then(html => {
                        container.innerHTML = html;
                    })
                    .catch(error => {
                        container.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Error loading flagged questions: ${error.message}
                            </div>
                        `;
                    });
            }

            // Handle form submission for knowledge contributions
            const knowledgeForm = document.getElementById('knowledge-form');
            if (knowledgeForm) {
                knowledgeForm.addEventListener('submit', function(e) {
                    // Make sure TinyMCE content is saved to the textarea before submitting
                    if (typeof tinymce !== 'undefined') {
                        const editor = tinymce.get('id_text_content');
                        if (editor) {
                            editor.save();
                        }
                    }
                });
            }
        }

        // Sidebar toggle functionality is now handled by sidebar-toggle.js
    }); // End DOMContentLoaded

 </script>

{% if assistant.assistant_type == 'community' %}
<!-- Load TinyMCE JavaScript using Django's form media -->
{{ form.media.js }}

<!-- Load TinyMCE word limit script -->
<script src="{% static 'js/tinymce-word-limit.js' %}"></script>
{% endif %}

<!-- Load chat interactions script for upvote and flag functionality -->
<script src="{% static 'js/chat-interactions.js' %}"></script>

<!-- Load rating modal functionality -->
<script>
// Define the base URL for the rating endpoint with leading slash to ensure absolute path
const rateAssistantBaseUrl = "/directory/rate-assistant/";
// Use the assistantId variable that's already defined in the outer scope
const rateAssistantUrl = rateAssistantBaseUrl + assistantId + '/';
console.log('Rating URL configured as:', rateAssistantUrl);
</script>
<script src="{% static 'js/rating-modal.js' %}"></script>

<!-- Like Button Functionality -->
<script>
// Global variables for modals
let currentModalItemId = null;
let currentModalItemType = null;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize handlers
    handleLikeButtons();
    handleFolderOptionsModal();
});

// Helper function to get CSRF token
function getCsrfToken() {
    try {
        // First try to get the token from the form
        const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
        if (csrfInput && csrfInput.value) {
            return csrfInput.value;
        }

        // Next try to get it from meta tag
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta && csrfMeta.getAttribute('content')) {
            return csrfMeta.getAttribute('content');
        }

        // Try to get from cookie as fallback
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('csrftoken=')) {
                return cookie.substring('csrftoken='.length, cookie.length);
            }
        }

        console.warn('CSRF token not found in form, meta tag, or cookies');
        return null;
    } catch (error) {
        console.error('Error getting CSRF token:', error);
        return null;
    }
}

// --- Like Button Logic ---
function handleLikeButtons() {
    const likeButtons = document.querySelectorAll('.like-button');
    if (!likeButtons.length) {
        console.log("No like buttons found on this page.");
        return;
    }

    console.log(`Found ${likeButtons.length} like buttons on the page`);

    likeButtons.forEach(button => {
        button.addEventListener('click', async function(event) {
            event.preventDefault();
            event.stopPropagation();

            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                console.error("CSRF token not found!");
                alert("Action failed. Please refresh.");
                return;
            }

            const itemId = this.getAttribute('data-item-id');
            const itemType = this.getAttribute('data-item-type');
            const url = "{% url 'directory:toggle_saved_item' %}";

            // Store for potential modal use
            currentModalItemId = itemId;
            currentModalItemType = itemType;

            try {
                console.log(`Sending like request for ${itemType} with ID ${itemId}`);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({ 'item_id': itemId, 'item_type': itemType })
                });

                console.log('Response status:', response.status);

                let responseText;
                try {
                    responseText = await response.text();
                    console.log('Raw response text:', responseText);
                    const data = JSON.parse(responseText);
                    console.log('Parsed response data:', data);

                    if (!response.ok) throw new Error(data.message || `HTTP error ${response.status}`);

                    if (data.status === 'success' && data.action === 'unfavorited') {
                        // Item was unfavorited successfully
                        updateHeartIcon(this, false);
                    } else if (data.status === 'options') {
                        // Item is not saved, show folder options modal
                        const folderModal = bootstrap.Modal.getInstance(document.getElementById('folderOptionsModal'));
                        if (folderModal) {
                            populateAndShowFolderModal(data);
                        } else {
                            // Fallback: If modal doesn't exist, just mark as liked (basic functionality)
                            console.warn("Folder modal not found, performing basic like.");
                            updateHeartIcon(this, true); // Assume it was liked if options were offered
                        }
                    } else if (data.status === 'success' && data.action === 'favorited') {
                        // Handle case where toggle immediately favorites (no modal needed)
                        updateHeartIcon(this, true);
                    } else {
                        // Unexpected success response
                        console.error('Unexpected response from toggle_saved_item:', data);
                        alert('An unexpected error occurred.');
                    }
                } catch (parseError) {
                    console.error('Error parsing JSON response:', parseError);
                    alert(`Failed to parse server response: ${parseError.message}`);
                }

            } catch (error) {
                console.error('Error handling favorite click:', error);
                alert(`An error occurred: ${error.message}`);
            }
        });
    });

    // Helper: Update Heart Icon
    function updateHeartIcon(button, isSaved) {
        if (!button) return;

        // Update the icon
        const icon = button.querySelector('i');
        if (icon) {
            if (isSaved) {
                icon.classList.remove('bi-heart');
                icon.classList.add('bi-heart-fill', 'text-danger', 'pulse');
                button.innerHTML = '<i class="bi bi-heart-fill text-danger me-1 pulse"></i> Favorited';

                // Remove pulse class after animation
                setTimeout(() => {
                    icon.classList.remove('pulse');
                }, 600);
            } else {
                icon.classList.remove('bi-heart-fill', 'text-danger', 'pulse');
                icon.classList.add('bi-heart');
                button.innerHTML = '<i class="bi bi-heart me-1"></i> Favorite';
            }
        }

        // Update button classes
        if (isSaved) {
            button.classList.remove('text-secondary');
            button.classList.add('text-danger');
            button.title = 'Remove from Favorites';
        } else {
            button.classList.remove('text-danger');
            button.classList.add('text-secondary');
            button.title = 'Add to Favorites';
        }
    }
}

// --- Folder Options Modal Logic ---
function handleFolderOptionsModal() {
    const folderModalElement = document.getElementById('folderOptionsModal');
    if (!folderModalElement) {
        console.log("Folder options modal not found.");
        return;
    }

    let folderModal = bootstrap.Modal.getInstance(folderModalElement);
    if (!folderModal) {
        try {
            folderModal = new bootstrap.Modal(folderModalElement);
        } catch (e) {
            console.error("Failed to initialize Bootstrap Modal for folders:", e);
            return;
        }
    }

    const modalItemName = folderModalElement.querySelector('#modalItemName');
    const folderOptionsList = folderModalElement.querySelector('#folderOptionsList');
    const createFolderToggleBtn = folderModalElement.querySelector('.create-folder-toggle-btn');
    const createFolderForm = folderModalElement.querySelector('#createFolderForm');
    const newFolderNameInput = folderModalElement.querySelector('#newFolderName');
    const saveWithoutFolderBtn = folderModalElement.querySelector('.save-without-folder-btn');
    const createAndSaveBtn = folderModalElement.querySelector('.create-and-save-btn');
    const folderModalErrorMsg = folderModalElement.querySelector('#folderModalErrorMsg');

    // Toggle create folder form
    if (createFolderToggleBtn && createFolderForm) {
        createFolderToggleBtn.addEventListener('click', function() {
            createFolderForm.style.display = createFolderForm.style.display === 'none' ? 'block' : 'none';
            if (createFolderForm.style.display === 'block') {
                newFolderNameInput.focus();
            }
        });
    }

    // Save without folder button
    if (saveWithoutFolderBtn) {
        saveWithoutFolderBtn.addEventListener('click', async function() {
            await saveToFavorites();
        });
    }

    // Create and save button
    if (createAndSaveBtn && newFolderNameInput) {
        createAndSaveBtn.addEventListener('click', async function() {
            const folderName = newFolderNameInput.value.trim();
            if (!folderName) {
                if (folderModalErrorMsg) {
                    folderModalErrorMsg.textContent = 'Please enter a folder name.';
                    folderModalErrorMsg.style.display = 'block';
                }
                return;
            }
            await saveToFavorites(folderName);
        });
    }

    // Handle folder click in the list
    if (folderOptionsList) {
        folderOptionsList.addEventListener('click', async function(event) {
            const folderItem = event.target.closest('.list-group-item');
            if (!folderItem) return;

            const folderId = folderItem.getAttribute('data-folder-id');
            await saveToFavorites(null, folderId);
        });
    }

    // Helper: Save to favorites function
    async function saveToFavorites(folderName = null, folderId = null) {
        if (!currentModalItemId || !currentModalItemType) {
            console.error("No item selected for saving.");
            return;
        }

        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            if (folderModalErrorMsg) {
                folderModalErrorMsg.textContent = 'CSRF token not found. Please refresh the page.';
                folderModalErrorMsg.style.display = 'block';
            }
            return;
        }

        // Disable buttons during save
        if (saveWithoutFolderBtn) saveWithoutFolderBtn.disabled = true;
        if (createAndSaveBtn) createAndSaveBtn.disabled = true;
        if (folderModalErrorMsg) folderModalErrorMsg.style.display = 'none';

        try {
            let url, formData;

            if (folderName) {
                // Create new folder and save
                url = "{% url 'directory:create_folder_and_save' %}";
                formData = new URLSearchParams({
                    'item_id': currentModalItemId,
                    'item_type': currentModalItemType,
                    'folder_name': folderName
                });
            } else if (folderId) {
                // Save to existing folder
                url = "{% url 'directory:add_item_to_folder' %}";
                formData = new URLSearchParams({
                    'item_id': currentModalItemId,
                    'item_type': currentModalItemType,
                    'folder_id': folderId
                });
            } else {
                // Save without folder
                url = "{% url 'directory:save_item_no_folder' %}";
                formData = new URLSearchParams({
                    'item_id': currentModalItemId,
                    'item_type': currentModalItemType
                });
            }

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Update all matching heart icons
                document.querySelectorAll(`.like-button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`).forEach(button => {
                    // Update the icon
                    const icon = button.querySelector('i');
                    if (icon) {
                        icon.classList.remove('bi-heart');
                        icon.classList.add('bi-heart-fill', 'text-danger');
                    }
                    button.innerHTML = '<i class="bi bi-heart-fill text-danger me-1"></i> Favorited';
                    button.classList.remove('text-secondary');
                    button.classList.add('text-danger');
                    button.title = 'Remove from Favorites';
                });

                // Close the modal
                const modalInstance = bootstrap.Modal.getInstance(folderModalElement);
                if (modalInstance) modalInstance.hide();

                // Reset form
                if (createFolderForm) createFolderForm.style.display = 'none';
                if (newFolderNameInput) newFolderNameInput.value = '';
            } else {
                if (folderModalErrorMsg) {
                    folderModalErrorMsg.textContent = data.message || 'Failed to save to favorites.';
                    folderModalErrorMsg.style.display = 'block';
                }
            }
        } catch (error) {
            console.error('Error saving to favorites:', error);
            if (folderModalErrorMsg) {
                folderModalErrorMsg.textContent = 'An unexpected error occurred.';
                folderModalErrorMsg.style.display = 'block';
            }
        } finally {
            // Re-enable buttons
            if (saveWithoutFolderBtn) saveWithoutFolderBtn.disabled = false;
            if (createAndSaveBtn) createAndSaveBtn.disabled = false;
        }
    }
}

// Helper: Populate and show folder modal
function populateAndShowFolderModal(data) {
    const folderModalElement = document.getElementById('folderOptionsModal');
    if (!folderModalElement) return;

    const modalItemName = folderModalElement.querySelector('#modalItemName');
    const folderOptionsList = folderModalElement.querySelector('#folderOptionsList');

    // Set item name
    if (modalItemName) modalItemName.textContent = data.item_name || 'this item';

    // Populate folder list
    if (folderOptionsList) {
        folderOptionsList.innerHTML = '';
        if (data.folders && data.folders.length > 0) {
            data.folders.forEach(folder => {
                const folderItem = document.createElement('button');
                folderItem.className = 'list-group-item list-group-item-action d-flex align-items-center';
                folderItem.setAttribute('data-folder-id', folder.id);
                folderItem.innerHTML = `
                    <i class="bi bi-folder me-2"></i>
                    <span>${folder.name}</span>
                `;
                folderOptionsList.appendChild(folderItem);
            });
        } else {
            folderOptionsList.innerHTML = '<div class="text-muted small p-2">No folders yet. Create one below.</div>';
        }
    }

    // Show the modal
    const folderModal = bootstrap.Modal.getInstance(folderModalElement) || new bootstrap.Modal(folderModalElement);
    folderModal.show();
}
</script>

<!-- Additional script to ensure chat scrolls properly -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get chat box element
        const chatBox = document.getElementById('chat-box');

        // Auto-scroll chat to bottom function
        const scrollChatToBottom = function() {
            if (chatBox) {
                setTimeout(() => {
                    chatBox.scrollTop = chatBox.scrollHeight;
                }, 100);
            }
        };

        // Mobile layout optimizations
        const optimizeMobileLayout = function() {
            if (window.innerWidth <= 768) {
                // Prevent body scroll on mobile
                document.body.style.overflow = 'hidden';
                document.documentElement.style.overflow = 'hidden';

                // Ensure fixed header is properly positioned
                const unifiedHeader = document.querySelector('.unified-chat-header');
                if (unifiedHeader) {
                    unifiedHeader.style.position = 'fixed';
                    unifiedHeader.style.top = '0';
                    unifiedHeader.style.left = '0';
                    unifiedHeader.style.right = '0';
                    unifiedHeader.style.zIndex = '1050';
                    unifiedHeader.style.width = '100%';
                }

                // Ensure fixed input is properly positioned
                const fixedInput = document.getElementById('fixed-chat-input');
                if (fixedInput) {
                    fixedInput.style.position = 'fixed';
                    fixedInput.style.bottom = '0';
                    fixedInput.style.left = '0';
                    fixedInput.style.right = '0';
                    fixedInput.style.zIndex = '1040';
                }

                // Ensure body has proper padding for fixed elements
                document.body.style.paddingTop = '70px';
                document.body.style.paddingBottom = '90px';

                // Scroll to bottom on mobile
                scrollChatToBottom();
            } else {
                // Re-enable body scroll on desktop
                document.body.style.overflow = '';
                document.documentElement.style.overflow = '';
                document.body.style.paddingTop = '';
                document.body.style.paddingBottom = '';
            }
        };

        // Initial setup
        if (chatBox) {
            // Scroll to bottom on page load
            scrollChatToBottom();

            // Set up mutation observer to auto-scroll when new messages are added
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        scrollChatToBottom();
                    }
                });
            });

            observer.observe(chatBox, { childList: true, subtree: true });
        }

        // Call mobile optimizations initially and on resize
        optimizeMobileLayout();
        window.addEventListener('resize', optimizeMobileLayout);

        // Mobile header functionality
        // Handle mobile QR code button
        const qrCodeBtnMobile = document.getElementById('qr-code-btn-mobile');
        if (qrCodeBtnMobile) {
            qrCodeBtnMobile.addEventListener('click', function() {
                // Copy the functionality from the desktop QR code button
                const assistantId = this.getAttribute('data-assistant-id');
                const qrUrl = this.getAttribute('data-qr-url');
                const isCompany = this.getAttribute('data-is-company');
                const companyName = this.getAttribute('data-company-name');

                // Set the QR code image and download link
                const qrCodeImage = document.getElementById('qrCodeImage');
                const qrCodeDownloadLink = document.getElementById('qrCodeDownloadLink');
                const qrCodeDescription = document.getElementById('qrCodeDescription');

                if (qrCodeImage && qrUrl) {
                    qrCodeImage.src = qrUrl;
                    if (qrCodeDownloadLink) {
                        qrCodeDownloadLink.href = qrUrl;
                    }
                    if (qrCodeDescription) {
                        if (isCompany && companyName) {
                            qrCodeDescription.textContent = `Scan this code to access ${companyName}.`;
                        } else {
                            qrCodeDescription.textContent = 'Scan this code to open the chat.';
                        }
                    }
                }
            });
        }

        // Handle mobile copy URL button
        const copyUrlBtnMobile = document.getElementById('copy-url-btn-mobile');
        if (copyUrlBtnMobile) {
            copyUrlBtnMobile.addEventListener('click', function() {
                copyUrlToClipboard(this);
            });
        }

        // Handle desktop QR code button
        const qrCodeBtnDesktop = document.getElementById('qr-code-btn-desktop');
        if (qrCodeBtnDesktop) {
            qrCodeBtnDesktop.addEventListener('click', function() {
                // Copy the functionality from the mobile QR code button
                const assistantId = this.getAttribute('data-assistant-id');
                const qrUrl = this.getAttribute('data-qr-url');
                const isCompany = this.getAttribute('data-is-company');
                const companyName = this.getAttribute('data-company-name');

                // Set the QR code image and download link
                const qrCodeImage = document.getElementById('qrCodeImage');
                const qrCodeDownloadLink = document.getElementById('qrCodeDownloadLink');
                const qrCodeDescription = document.getElementById('qrCodeDescription');

                if (qrCodeImage && qrUrl) {
                    qrCodeImage.src = qrUrl;
                    if (qrCodeDownloadLink) {
                        qrCodeDownloadLink.href = qrUrl;
                    }
                    if (qrCodeDescription) {
                        if (isCompany && companyName) {
                            qrCodeDescription.textContent = `Scan this code to access ${companyName}.`;
                        } else {
                            qrCodeDescription.textContent = 'Scan this code to open the chat.';
                        }
                    }
                }
            });
        }

        // Handle desktop copy URL button
        const copyUrlBtnDesktop = document.getElementById('copy-url-btn-desktop');
        if (copyUrlBtnDesktop) {
            copyUrlBtnDesktop.addEventListener('click', function() {
                copyUrlToClipboard(this);
            });
        }

        // Shared function to copy URL to clipboard
        function copyUrlToClipboard(button) {
            const currentUrl = window.location.href;

            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(currentUrl).then(function() {
                    // Show success feedback
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="bi bi-check me-1"></i> Copied!';
                    button.classList.add('text-success');

                    setTimeout(function() {
                        button.innerHTML = originalText;
                        button.classList.remove('text-success');
                    }, 2000);
                }).catch(function(err) {
                    console.error('Failed to copy URL: ', err);
                    // Fallback for older browsers
                    fallbackCopyTextToClipboard(currentUrl, button);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(currentUrl, button);
            }
        }

        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="bi bi-check me-1"></i> Copied!';
                    button.classList.add('text-success');

                    setTimeout(function() {
                        button.innerHTML = originalText;
                        button.classList.remove('text-success');
                    }, 2000);
                }
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
            }

            document.body.removeChild(textArea);
        }
    });
</script>
{% endblock extra_js %}



{# Folder Options Modal for Favorites #}
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="folderOptionsModalLabel">Save to Favorites</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Save <strong id="modalItemName">this item</strong> to:</p>

        {# Quick Save Option #}
        <div class="d-grid gap-2 mb-3">
          <button type="button" class="btn btn-primary save-without-folder-btn">
            <i class="bi bi-heart-fill me-2"></i> Save to Favorites
          </button>
        </div>

        {# Folder Options #}
        <div class="folder-options-container">
          <h6 class="mb-2">Or select a folder:</h6>
          <div id="folderOptionsList" class="list-group mb-3">
            {# Folders will be populated by JavaScript #}
          </div>
        </div>

        {# Create New Folder Option #}
        <div class="create-folder-container">
          <div class="d-grid gap-2">
            <button type="button" class="btn btn-outline-secondary create-folder-toggle-btn">
              <i class="bi bi-folder-plus me-2"></i> Create New Folder
            </button>
          </div>

          <div id="createFolderForm" class="mt-3" style="display: none;">
            <div class="mb-3">
              <label for="newFolderName" class="form-label">Folder Name</label>
              <input type="text" class="form-control" id="newFolderName" placeholder="Enter folder name">
            </div>
            <div class="d-grid gap-2">
              <button type="button" class="btn btn-success create-and-save-btn">
                <i class="bi bi-check-circle me-2"></i> Create Folder & Save
              </button>
            </div>
          </div>
        </div>

        <div id="folderModalErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
      </div>
    </div>
  </div>
</div>
{# End Folder Options Modal #}

{% endblock content %}

